console.log('开始测试...');

try {
  console.log('1. 测试基本Node.js功能...');
  console.log('   ✅ Node.js正常工作');
  
  console.log('2. 测试环境变量加载...');
  require('dotenv').config();
  console.log('   ✅ dotenv加载成功');
  
  console.log('3. 测试OpenAI配置模块...');
  const openaiConfig = require('./config/openai-config');
  console.log('   ✅ OpenAI配置模块加载成功');
  
  console.log('4. 测试服务模块导入...');
  
  console.log('   测试PDF服务...');
  const pdfApp = require('./pdf-extraction-service');
  console.log('   ✅ PDF服务导入成功');
  
  console.log('   测试PineCone服务...');
  const { PineconeTranslationService } = require('./pinecone-service');
  console.log('   ✅ PineCone服务导入成功');
  
  console.log('   测试翻译服务...');
  const { MultiAgentTranslator } = require('./multi-agent-translator');
  console.log('   ✅ 翻译服务导入成功');
  
  console.log('   测试文档处理服务...');
  const { DocumentProcessor } = require('./document-processor');
  console.log('   ✅ 文档处理服务导入成功');
  
  console.log('   测试主服务...');
  const TranslationWorkflowManager = require('./index');
  console.log('   ✅ 主服务导入成功');
  
  console.log('\n🎉 所有测试通过！');
  
} catch (error) {
  console.error('\n❌ 测试失败:', error.message);
  console.error('错误堆栈:', error.stack);
  process.exit(1);
}
