# OpenAI配置指南

本文档详细说明如何配置OpenAI API以支持自定义Base URL。

## 🔧 基础配置

### 1. 环境变量设置

在 `.env` 文件中设置以下变量：

```env
# 必需配置
OPENAI_API_KEY=your_openai_api_key_here

# 可选配置 - Base URL
OPENAI_BASE_URL=https://api.openai.com/v1

# 可选配置 - 模型参数
OPENAI_MODEL=gpt-4
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
OPENAI_TEMPERATURE=0.3
OPENAI_MAX_TOKENS=2000
```

### 2. Base URL配置场景

#### 官方API (默认)
```env
OPENAI_BASE_URL=https://api.openai.com/v1
```

#### 使用代理服务
```env
# 示例代理服务
OPENAI_BASE_URL=https://your-proxy-domain.com/v1
OPENAI_BASE_URL=https://api.openai-proxy.com/v1
OPENAI_BASE_URL=https://openai.example.com/v1
```

#### Azure OpenAI服务
```env
OPENAI_BASE_URL=https://your-resource.openai.azure.com/openai/deployments/your-deployment
```

#### 本地部署的OpenAI兼容服务
```env
OPENAI_BASE_URL=http://localhost:8000/v1
OPENAI_BASE_URL=http://your-local-server:8080/v1
```

## 🧪 配置测试

### 快速测试
```bash
npm run test:openai
```

### 手动测试
```bash
node test-openai-config.js
```

测试工具会验证：
- ✅ API密钥有效性
- ✅ Base URL连接性
- ✅ 聊天完成API功能
- ✅ 文本嵌入API功能

## 🔍 常见问题排查

### 1. API密钥问题

**错误**: `401 Unauthorized`
```
❌ API连接测试失败
💡 建议: 请检查OPENAI_API_KEY是否正确
```

**解决方案**:
- 确认API密钥格式正确 (sk-...)
- 检查密钥是否有效且未过期
- 验证密钥权限是否足够

### 2. Base URL连接问题

**错误**: `ECONNREFUSED` 或 `timeout`
```
❌ OpenAI API连接测试失败
💡 建议: 请检查网络连接或OPENAI_BASE_URL配置
```

**解决方案**:
- 检查Base URL是否正确
- 验证网络连接
- 确认代理服务是否正常运行
- 检查防火墙设置

### 3. 模型不可用

**错误**: `model not found`
```
❌ 聊天完成API测试失败
```

**解决方案**:
- 检查模型名称是否正确
- 确认账户是否有权限使用该模型
- 尝试使用其他可用模型

### 4. 配额限制

**错误**: `rate_limit_exceeded`
```
❌ API调用频率过高，请稍后重试
💡 建议: 检查账户配额是否充足
```

**解决方案**:
- 等待一段时间后重试
- 检查账户余额
- 升级API计划

## ⚙️ 高级配置

### 1. 重试机制配置

系统内置了智能重试机制，可以通过环境变量调整：

```env
# 重试相关配置 (可选)
OPENAI_MAX_RETRIES=3
OPENAI_RETRY_DELAY=1000
OPENAI_TIMEOUT=30000
```

### 2. 模型参数调优

根据翻译需求调整模型参数：

```env
# 翻译质量优化
OPENAI_TEMPERATURE=0.3      # 较低温度确保一致性
OPENAI_MAX_TOKENS=2000      # 足够的输出长度
OPENAI_TOP_P=1              # 完整的词汇选择
OPENAI_FREQUENCY_PENALTY=0  # 不惩罚重复
OPENAI_PRESENCE_PENALTY=0   # 不惩罚主题重复
```

### 3. 性能优化

```env
# 性能相关配置
OPENAI_BATCH_SIZE=10        # 批处理大小
OPENAI_CONCURRENT_REQUESTS=3 # 并发请求数
```

## 🔒 安全最佳实践

### 1. API密钥安全

- ❌ 不要在代码中硬编码API密钥
- ❌ 不要将.env文件提交到版本控制
- ✅ 使用环境变量或密钥管理服务
- ✅ 定期轮换API密钥

### 2. 网络安全

```bash
# 设置文件权限
chmod 600 .env

# 使用HTTPS
OPENAI_BASE_URL=https://your-secure-proxy.com/v1
```

### 3. 生产环境配置

```env
# 生产环境建议
OPENAI_TIMEOUT=60000        # 更长的超时时间
OPENAI_MAX_RETRIES=5        # 更多重试次数
LOG_LEVEL=error             # 减少日志输出
```

## 📊 监控和日志

### 1. API调用监控

系统会自动记录：
- API调用次数
- 响应时间
- 错误率
- Token使用量

### 2. 日志配置

```env
# 日志级别
LOG_LEVEL=info              # debug, info, warn, error

# 日志文件
LOG_FILE=./logs/openai.log
```

## 🌐 代理服务推荐

### 1. 自建代理

如果需要自建OpenAI代理服务，推荐使用：
- [openai-proxy](https://github.com/stulzq/openai-proxy)
- [ChatGPT-Proxy](https://github.com/dqzboy/ChatGPT-Proxy)

### 2. 第三方服务

选择可信的第三方代理服务时，注意：
- 服务稳定性
- 数据安全性
- 价格合理性
- 技术支持

## 🆘 获取帮助

如果遇到配置问题：

1. **运行诊断工具**:
   ```bash
   npm run test:openai
   ```

2. **查看详细日志**:
   ```bash
   tail -f ./logs/openai.log
   ```

3. **检查网络连接**:
   ```bash
   curl -I https://api.openai.com/v1/models
   ```

4. **联系支持**:
   - 查看项目Issues
   - 提交新的Issue
   - 联系技术支持

## 📝 配置模板

### 基础模板
```env
OPENAI_API_KEY=sk-your-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4
```

### 代理服务模板
```env
OPENAI_API_KEY=sk-your-key-here
OPENAI_BASE_URL=https://your-proxy.com/v1
OPENAI_MODEL=gpt-4
OPENAI_TIMEOUT=60000
OPENAI_MAX_RETRIES=5
```

### 开发环境模板
```env
OPENAI_API_KEY=sk-your-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_TEMPERATURE=0.5
LOG_LEVEL=debug
```
