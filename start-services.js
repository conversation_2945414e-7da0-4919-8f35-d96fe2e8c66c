#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class ServiceManager {
  constructor() {
    this.services = [
      {
        name: 'PDF提取服务',
        script: 'pdf-extraction-service.js',
        port: 3001,
        process: null
      },
      {
        name: 'PineCone服务',
        script: 'pinecone-service.js',
        port: 3002,
        process: null
      },
      {
        name: '多智能体翻译服务',
        script: 'multi-agent-translator.js',
        port: 3003,
        process: null
      },
      {
        name: '文档处理服务',
        script: 'document-processor.js',
        port: 3004,
        process: null
      },
      {
        name: '主服务',
        script: 'index.js',
        port: 3000,
        process: null
      }
    ];

    this.isShuttingDown = false;
  }

  async checkEnvironment() {
    console.log('🔍 检查环境配置...');

    // 检查.env文件
    if (!fs.existsSync('.env')) {
      console.log('⚠️  未找到.env文件，请复制.env.example并配置API密钥');
      return false;
    }

    // 检查OpenAI配置
    try {
      const openaiConfig = require('./config/openai-config');
      console.log('🔧 验证OpenAI配置...');

      const configInfo = openaiConfig.getConfigInfo();
      if (!configInfo.hasApiKey) {
        console.log('❌ 未设置OPENAI_API_KEY环境变量');
        console.log('💡 请在.env文件中设置您的OpenAI API密钥');
        return false;
      }

      console.log(`   Base URL: ${configInfo.baseURL}`);
      console.log(`   模型: ${configInfo.model}`);

      // 测试API连接
      const connectionSuccess = await openaiConfig.testConnection();
      if (!connectionSuccess) {
        console.log('❌ OpenAI API连接测试失败');
        console.log('💡 请运行 npm run test:openai 进行详细诊断');
        return false;
      }

      console.log('✅ OpenAI配置验证成功');
    } catch (error) {
      console.log(`❌ OpenAI配置检查失败: ${error.message}`);
      console.log('💡 请运行 npm run test:openai 进行详细诊断');
      return false;
    }

    // 检查必要目录
    const dirs = ['uploads', 'outputs', 'logs'];
    for (const dir of dirs) {
      if (!fs.existsSync(dir)) {
        console.log(`📁 创建目录: ${dir}`);
        fs.mkdirSync(dir, { recursive: true });
      }
    }

    // 检查Node.js版本
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 18) {
      console.log(`⚠️  Node.js版本过低 (${nodeVersion})，建议使用18.0.0或更高版本`);
    }

    console.log('✅ 环境检查完成');
    return true;
  }

  async startService(service) {
    return new Promise((resolve, reject) => {
      console.log(`🚀 启动 ${service.name} (端口 ${service.port})...`);

      const process = spawn('node', [service.script], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, PORT: service.port }
      });

      service.process = process;

      // 处理输出
      process.stdout.on('data', (data) => {
        console.log(`[${service.name}] ${data.toString().trim()}`);
      });

      process.stderr.on('data', (data) => {
        console.error(`[${service.name}] ERROR: ${data.toString().trim()}`);
      });

      // 处理进程退出
      process.on('exit', (code) => {
        if (!this.isShuttingDown) {
          console.log(`❌ ${service.name} 意外退出 (代码: ${code})`);
          if (code !== 0) {
            reject(new Error(`${service.name} 启动失败`));
          }
        }
      });

      // 等待服务启动
      setTimeout(() => {
        if (process.pid) {
          console.log(`✅ ${service.name} 启动成功 (PID: ${process.pid})`);
          resolve();
        } else {
          reject(new Error(`${service.name} 启动超时`));
        }
      }, 3000);
    });
  }

  async startAllServices() {
    console.log('🎯 启动所有翻译服务...\n');

    try {
      // 检查环境
      const envOk = await this.checkEnvironment();
      if (!envOk) {
        process.exit(1);
      }

      // 按顺序启动服务（除了主服务）
      for (const service of this.services.slice(0, -1)) {
        await this.startService(service);
        await this.delay(2000); // 等待2秒再启动下一个服务
      }

      // 最后启动主服务
      await this.startService(this.services[this.services.length - 1]);

      console.log('\n🎉 所有服务启动完成！');
      console.log('\n📋 服务状态:');
      this.services.forEach(service => {
        console.log(`   ${service.name}: http://localhost:${service.port}`);
      });

      console.log('\n🌐 访问地址:');
      console.log('   主服务: http://localhost:3000');
      console.log('   健康检查: http://localhost:3000/health');
      console.log('   工作流状态: http://localhost:3000/api/workflow/status');

      console.log('\n💡 提示:');
      console.log('   - 按 Ctrl+C 停止所有服务');
      console.log('   - 查看 ./logs/ 目录获取详细日志');
      console.log('   - 导入 n8n-translation-workflow.json 到N8N中使用');

    } catch (error) {
      console.error(`❌ 服务启动失败: ${error.message}`);
      await this.stopAllServices();
      process.exit(1);
    }
  }

  async stopAllServices() {
    if (this.isShuttingDown) return;

    this.isShuttingDown = true;
    console.log('\n🛑 正在停止所有服务...');

    for (const service of this.services) {
      if (service.process && service.process.pid) {
        console.log(`   停止 ${service.name}...`);
        try {
          service.process.kill('SIGTERM');
          await this.delay(1000);

          if (service.process.killed === false) {
            service.process.kill('SIGKILL');
          }
        } catch (error) {
          console.error(`   停止 ${service.name} 时出错: ${error.message}`);
        }
      }
    }

    console.log('✅ 所有服务已停止');
    process.exit(0);
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  setupSignalHandlers() {
    // 处理Ctrl+C
    process.on('SIGINT', () => {
      console.log('\n收到停止信号...');
      this.stopAllServices();
    });

    // 处理终止信号
    process.on('SIGTERM', () => {
      console.log('\n收到终止信号...');
      this.stopAllServices();
    });

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      console.error('未捕获的异常:', error);
      this.stopAllServices();
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('未处理的Promise拒绝:', reason);
      this.stopAllServices();
    });
  }
}

// 主函数
async function main() {
  const manager = new ServiceManager();

  // 设置信号处理
  manager.setupSignalHandlers();

  // 显示启动信息
  console.log('🔥 N8N精准文档翻译工作流');
  console.log('=====================================');
  console.log('版本: 1.0.0');
  console.log('作者: AI Assistant');
  console.log('=====================================\n');

  // 启动所有服务
  await manager.startAllServices();

  // 保持进程运行
  process.stdin.resume();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('启动失败:', error);
    process.exit(1);
  });
}

module.exports = ServiceManager;
