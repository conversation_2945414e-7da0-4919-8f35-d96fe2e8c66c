{"name": "langchain", "version": "0.1.37", "description": "Typescript bindings for langchain", "type": "module", "engines": {"node": ">=18"}, "main": "./index.js", "types": "./index.d.ts", "files": ["dist/", "load.cjs", "load.js", "load.d.ts", "load.d.cts", "load/serializable.cjs", "load/serializable.js", "load/serializable.d.ts", "load/serializable.d.cts", "agents.cjs", "agents.js", "agents.d.ts", "agents.d.cts", "agents/load.cjs", "agents/load.js", "agents/load.d.ts", "agents/load.d.cts", "agents/toolkits.cjs", "agents/toolkits.js", "agents/toolkits.d.ts", "agents/toolkits.d.cts", "agents/toolkits/aws_sfn.cjs", "agents/toolkits/aws_sfn.js", "agents/toolkits/aws_sfn.d.ts", "agents/toolkits/aws_sfn.d.cts", "agents/toolkits/connery.cjs", "agents/toolkits/connery.js", "agents/toolkits/connery.d.ts", "agents/toolkits/connery.d.cts", "agents/toolkits/sql.cjs", "agents/toolkits/sql.js", "agents/toolkits/sql.d.ts", "agents/toolkits/sql.d.cts", "agents/format_scratchpad.cjs", "agents/format_scratchpad.js", "agents/format_scratchpad.d.ts", "agents/format_scratchpad.d.cts", "agents/format_scratchpad/openai_tools.cjs", "agents/format_scratchpad/openai_tools.js", "agents/format_scratchpad/openai_tools.d.ts", "agents/format_scratchpad/openai_tools.d.cts", "agents/format_scratchpad/log.cjs", "agents/format_scratchpad/log.js", "agents/format_scratchpad/log.d.ts", "agents/format_scratchpad/log.d.cts", "agents/format_scratchpad/xml.cjs", "agents/format_scratchpad/xml.js", "agents/format_scratchpad/xml.d.ts", "agents/format_scratchpad/xml.d.cts", "agents/format_scratchpad/log_to_message.cjs", "agents/format_scratchpad/log_to_message.js", "agents/format_scratchpad/log_to_message.d.ts", "agents/format_scratchpad/log_to_message.d.cts", "agents/react/output_parser.cjs", "agents/react/output_parser.js", "agents/react/output_parser.d.ts", "agents/react/output_parser.d.cts", "agents/xml/output_parser.cjs", "agents/xml/output_parser.js", "agents/xml/output_parser.d.ts", "agents/xml/output_parser.d.cts", "agents/openai/output_parser.cjs", "agents/openai/output_parser.js", "agents/openai/output_parser.d.ts", "agents/openai/output_parser.d.cts", "base_language.cjs", "base_language.js", "base_language.d.ts", "base_language.d.cts", "tools.cjs", "tools.js", "tools.d.ts", "tools.d.cts", "tools/aws_lambda.cjs", "tools/aws_lambda.js", "tools/aws_lambda.d.ts", "tools/aws_lambda.d.cts", "tools/aws_sfn.cjs", "tools/aws_sfn.js", "tools/aws_sfn.d.ts", "tools/aws_sfn.d.cts", "tools/calculator.cjs", "tools/calculator.js", "tools/calculator.d.ts", "tools/calculator.d.cts", "tools/chain.cjs", "tools/chain.js", "tools/chain.d.ts", "tools/chain.d.cts", "tools/connery.cjs", "tools/connery.js", "tools/connery.d.ts", "tools/connery.d.cts", "tools/render.cjs", "tools/render.js", "tools/render.d.ts", "tools/render.d.cts", "tools/retriever.cjs", "tools/retriever.js", "tools/retriever.d.ts", "tools/retriever.d.cts", "tools/sql.cjs", "tools/sql.js", "tools/sql.d.ts", "tools/sql.d.cts", "tools/webbrowser.cjs", "tools/webbrowser.js", "tools/webbrowser.d.ts", "tools/webbrowser.d.cts", "tools/gmail.cjs", "tools/gmail.js", "tools/gmail.d.ts", "tools/gmail.d.cts", "tools/google_calendar.cjs", "tools/google_calendar.js", "tools/google_calendar.d.ts", "tools/google_calendar.d.cts", "tools/google_places.cjs", "tools/google_places.js", "tools/google_places.d.ts", "tools/google_places.d.cts", "chains.cjs", "chains.js", "chains.d.ts", "chains.d.cts", "chains/combine_documents.cjs", "chains/combine_documents.js", "chains/combine_documents.d.ts", "chains/combine_documents.d.cts", "chains/combine_documents/reduce.cjs", "chains/combine_documents/reduce.js", "chains/combine_documents/reduce.d.ts", "chains/combine_documents/reduce.d.cts", "chains/history_aware_retriever.cjs", "chains/history_aware_retriever.js", "chains/history_aware_retriever.d.ts", "chains/history_aware_retriever.d.cts", "chains/load.cjs", "chains/load.js", "chains/load.d.ts", "chains/load.d.cts", "chains/openai_functions.cjs", "chains/openai_functions.js", "chains/openai_functions.d.ts", "chains/openai_functions.d.cts", "chains/query_constructor.cjs", "chains/query_constructor.js", "chains/query_constructor.d.ts", "chains/query_constructor.d.cts", "chains/query_constructor/ir.cjs", "chains/query_constructor/ir.js", "chains/query_constructor/ir.d.ts", "chains/query_constructor/ir.d.cts", "chains/retrieval.cjs", "chains/retrieval.js", "chains/retrieval.d.ts", "chains/retrieval.d.cts", "chains/sql_db.cjs", "chains/sql_db.js", "chains/sql_db.d.ts", "chains/sql_db.d.cts", "chains/graph_qa/cypher.cjs", "chains/graph_qa/cypher.js", "chains/graph_qa/cypher.d.ts", "chains/graph_qa/cypher.d.cts", "embeddings/base.cjs", "embeddings/base.js", "embeddings/base.d.ts", "embeddings/base.d.cts", "embeddings/bedrock.cjs", "embeddings/bedrock.js", "embeddings/bedrock.d.ts", "embeddings/bedrock.d.cts", "embeddings/cache_backed.cjs", "embeddings/cache_backed.js", "embeddings/cache_backed.d.ts", "embeddings/cache_backed.d.cts", "embeddings/cloudflare_workersai.cjs", "embeddings/cloudflare_workersai.js", "embeddings/cloudflare_workersai.d.ts", "embeddings/cloudflare_workersai.d.cts", "embeddings/fake.cjs", "embeddings/fake.js", "embeddings/fake.d.ts", "embeddings/fake.d.cts", "embeddings/ollama.cjs", "embeddings/ollama.js", "embeddings/ollama.d.ts", "embeddings/ollama.d.cts", "embeddings/openai.cjs", "embeddings/openai.js", "embeddings/openai.d.ts", "embeddings/openai.d.cts", "embeddings/cohere.cjs", "embeddings/cohere.js", "embeddings/cohere.d.ts", "embeddings/cohere.d.cts", "embeddings/tensorflow.cjs", "embeddings/tensorflow.js", "embeddings/tensorflow.d.ts", "embeddings/tensorflow.d.cts", "embeddings/hf.cjs", "embeddings/hf.js", "embeddings/hf.d.ts", "embeddings/hf.d.cts", "embeddings/hf_transformers.cjs", "embeddings/hf_transformers.js", "embeddings/hf_transformers.d.ts", "embeddings/hf_transformers.d.cts", "embeddings/googlevertexai.cjs", "embeddings/googlevertexai.js", "embeddings/googlevertexai.d.ts", "embeddings/googlevertexai.d.cts", "embeddings/googlepalm.cjs", "embeddings/googlepalm.js", "embeddings/googlepalm.d.ts", "embeddings/googlepalm.d.cts", "embeddings/minimax.cjs", "embeddings/minimax.js", "embeddings/minimax.d.ts", "embeddings/minimax.d.cts", "embeddings/voyage.cjs", "embeddings/voyage.js", "embeddings/voyage.d.ts", "embeddings/voyage.d.cts", "embeddings/llama_cpp.cjs", "embeddings/llama_cpp.js", "embeddings/llama_cpp.d.ts", "embeddings/llama_cpp.d.cts", "embeddings/gradient_ai.cjs", "embeddings/gradient_ai.js", "embeddings/gradient_ai.d.ts", "embeddings/gradient_ai.d.cts", "llms/load.cjs", "llms/load.js", "llms/load.d.ts", "llms/load.d.cts", "llms/base.cjs", "llms/base.js", "llms/base.d.ts", "llms/base.d.cts", "llms/openai.cjs", "llms/openai.js", "llms/openai.d.ts", "llms/openai.d.cts", "llms/ai21.cjs", "llms/ai21.js", "llms/ai21.d.ts", "llms/ai21.d.cts", "llms/aleph_alpha.cjs", "llms/aleph_alpha.js", "llms/aleph_alpha.d.ts", "llms/aleph_alpha.d.cts", "llms/cloudflare_workersai.cjs", "llms/cloudflare_workersai.js", "llms/cloudflare_workersai.d.ts", "llms/cloudflare_workersai.d.cts", "llms/cohere.cjs", "llms/cohere.js", "llms/cohere.d.ts", "llms/cohere.d.cts", "llms/hf.cjs", "llms/hf.js", "llms/hf.d.ts", "llms/hf.d.cts", "llms/raycast.cjs", "llms/raycast.js", "llms/raycast.d.ts", "llms/raycast.d.cts", "llms/ollama.cjs", "llms/ollama.js", "llms/ollama.d.ts", "llms/ollama.d.cts", "llms/replicate.cjs", "llms/replicate.js", "llms/replicate.d.ts", "llms/replicate.d.cts", "llms/fireworks.cjs", "llms/fireworks.js", "llms/fireworks.d.ts", "llms/fireworks.d.cts", "llms/googlevertexai.cjs", "llms/googlevertexai.js", "llms/googlevertexai.d.ts", "llms/googlevertexai.d.cts", "llms/googlevertexai/web.cjs", "llms/googlevertexai/web.js", "llms/googlevertexai/web.d.ts", "llms/googlevertexai/web.d.cts", "llms/googlepalm.cjs", "llms/googlepalm.js", "llms/googlepalm.d.ts", "llms/googlepalm.d.cts", "llms/gradient_ai.cjs", "llms/gradient_ai.js", "llms/gradient_ai.d.ts", "llms/gradient_ai.d.cts", "llms/sagemaker_endpoint.cjs", "llms/sagemaker_endpoint.js", "llms/sagemaker_endpoint.d.ts", "llms/sagemaker_endpoint.d.cts", "llms/watsonx_ai.cjs", "llms/watsonx_ai.js", "llms/watsonx_ai.d.ts", "llms/watsonx_ai.d.cts", "llms/bedrock.cjs", "llms/bedrock.js", "llms/bedrock.d.ts", "llms/bedrock.d.cts", "llms/bedrock/web.cjs", "llms/bedrock/web.js", "llms/bedrock/web.d.ts", "llms/bedrock/web.d.cts", "llms/llama_cpp.cjs", "llms/llama_cpp.js", "llms/llama_cpp.d.ts", "llms/llama_cpp.d.cts", "llms/writer.cjs", "llms/writer.js", "llms/writer.d.ts", "llms/writer.d.cts", "llms/portkey.cjs", "llms/portkey.js", "llms/portkey.d.ts", "llms/portkey.d.cts", "llms/yandex.cjs", "llms/yandex.js", "llms/yandex.d.ts", "llms/yandex.d.cts", "llms/fake.cjs", "llms/fake.js", "llms/fake.d.ts", "llms/fake.d.cts", "prompts.cjs", "prompts.js", "prompts.d.ts", "prompts.d.cts", "prompts/load.cjs", "prompts/load.js", "prompts/load.d.ts", "prompts/load.d.cts", "vectorstores/clickhouse.cjs", "vectorstores/clickhouse.js", "vectorstores/clickhouse.d.ts", "vectorstores/clickhouse.d.cts", "vectorstores/analyticdb.cjs", "vectorstores/analyticdb.js", "vectorstores/analyticdb.d.ts", "vectorstores/analyticdb.d.cts", "vectorstores/base.cjs", "vectorstores/base.js", "vectorstores/base.d.ts", "vectorstores/base.d.cts", "vectorstores/cassandra.cjs", "vectorstores/cassandra.js", "vectorstores/cassandra.d.ts", "vectorstores/cassandra.d.cts", "vectorstores/convex.cjs", "vectorstores/convex.js", "vectorstores/convex.d.ts", "vectorstores/convex.d.cts", "vectorstores/elasticsearch.cjs", "vectorstores/elasticsearch.js", "vectorstores/elasticsearch.d.ts", "vectorstores/elasticsearch.d.cts", "vectorstores/memory.cjs", "vectorstores/memory.js", "vectorstores/memory.d.ts", "vectorstores/memory.d.cts", "vectorstores/cloudflare_vectorize.cjs", "vectorstores/cloudflare_vectorize.js", "vectorstores/cloudflare_vectorize.d.ts", "vectorstores/cloudflare_vectorize.d.cts", "vectorstores/closevector/web.cjs", "vectorstores/closevector/web.js", "vectorstores/closevector/web.d.ts", "vectorstores/closevector/web.d.cts", "vectorstores/closevector/node.cjs", "vectorstores/closevector/node.js", "vectorstores/closevector/node.d.ts", "vectorstores/closevector/node.d.cts", "vectorstores/chroma.cjs", "vectorstores/chroma.js", "vectorstores/chroma.d.ts", "vectorstores/chroma.d.cts", "vectorstores/googlevertexai.cjs", "vectorstores/googlevertexai.js", "vectorstores/googlevertexai.d.ts", "vectorstores/googlevertexai.d.cts", "vectorstores/hnswlib.cjs", "vectorstores/hnswlib.js", "vectorstores/hnswlib.d.ts", "vectorstores/hnswlib.d.cts", "vectorstores/faiss.cjs", "vectorstores/faiss.js", "vectorstores/faiss.d.ts", "vectorstores/faiss.d.cts", "vectorstores/weaviate.cjs", "vectorstores/weaviate.js", "vectorstores/weaviate.d.ts", "vectorstores/weaviate.d.cts", "vectorstores/lancedb.cjs", "vectorstores/lancedb.js", "vectorstores/lancedb.d.ts", "vectorstores/lancedb.d.cts", "vectorstores/momento_vector_index.cjs", "vectorstores/momento_vector_index.js", "vectorstores/momento_vector_index.d.ts", "vectorstores/momento_vector_index.d.cts", "vectorstores/mongo.cjs", "vectorstores/mongo.js", "vectorstores/mongo.d.ts", "vectorstores/mongo.d.cts", "vectorstores/mongodb_atlas.cjs", "vectorstores/mongodb_atlas.js", "vectorstores/mongodb_atlas.d.ts", "vectorstores/mongodb_atlas.d.cts", "vectorstores/pinecone.cjs", "vectorstores/pinecone.js", "vectorstores/pinecone.d.ts", "vectorstores/pinecone.d.cts", "vectorstores/qdrant.cjs", "vectorstores/qdrant.js", "vectorstores/qdrant.d.ts", "vectorstores/qdrant.d.cts", "vectorstores/supabase.cjs", "vectorstores/supabase.js", "vectorstores/supabase.d.ts", "vectorstores/supabase.d.cts", "vectorstores/opensearch.cjs", "vectorstores/opensearch.js", "vectorstores/opensearch.d.ts", "vectorstores/opensearch.d.cts", "vectorstores/pgvector.cjs", "vectorstores/pgvector.js", "vectorstores/pgvector.d.ts", "vectorstores/pgvector.d.cts", "vectorstores/milvus.cjs", "vectorstores/milvus.js", "vectorstores/milvus.d.ts", "vectorstores/milvus.d.cts", "vectorstores/neo4j_vector.cjs", "vectorstores/neo4j_vector.js", "vectorstores/neo4j_vector.d.ts", "vectorstores/neo4j_vector.d.cts", "vectorstores/prisma.cjs", "vectorstores/prisma.js", "vectorstores/prisma.d.ts", "vectorstores/prisma.d.cts", "vectorstores/typeorm.cjs", "vectorstores/typeorm.js", "vectorstores/typeorm.d.ts", "vectorstores/typeorm.d.cts", "vectorstores/myscale.cjs", "vectorstores/myscale.js", "vectorstores/myscale.d.ts", "vectorstores/myscale.d.cts", "vectorstores/redis.cjs", "vectorstores/redis.js", "vectorstores/redis.d.ts", "vectorstores/redis.d.cts", "vectorstores/rockset.cjs", "vectorstores/rockset.js", "vectorstores/rockset.d.ts", "vectorstores/rockset.d.cts", "vectorstores/typesense.cjs", "vectorstores/typesense.js", "vectorstores/typesense.d.ts", "vectorstores/typesense.d.cts", "vectorstores/singlestore.cjs", "vectorstores/singlestore.js", "vectorstores/singlestore.d.ts", "vectorstores/singlestore.d.cts", "vectorstores/tigris.cjs", "vectorstores/tigris.js", "vectorstores/tigris.d.ts", "vectorstores/tigris.d.cts", "vectorstores/usearch.cjs", "vectorstores/usearch.js", "vectorstores/usearch.d.ts", "vectorstores/usearch.d.cts", "vectorstores/vectara.cjs", "vectorstores/vectara.js", "vectorstores/vectara.d.ts", "vectorstores/vectara.d.cts", "vectorstores/vercel_postgres.cjs", "vectorstores/vercel_postgres.js", "vectorstores/vercel_postgres.d.ts", "vectorstores/vercel_postgres.d.cts", "vectorstores/voy.cjs", "vectorstores/voy.js", "vectorstores/voy.d.ts", "vectorstores/voy.d.cts", "vectorstores/xata.cjs", "vectorstores/xata.js", "vectorstores/xata.d.ts", "vectorstores/xata.d.cts", "vectorstores/zep.cjs", "vectorstores/zep.js", "vectorstores/zep.d.ts", "vectorstores/zep.d.cts", "text_splitter.cjs", "text_splitter.js", "text_splitter.d.ts", "text_splitter.d.cts", "memory.cjs", "memory.js", "memory.d.ts", "memory.d.cts", "memory/zep.cjs", "memory/zep.js", "memory/zep.d.ts", "memory/zep.d.cts", "document.cjs", "document.js", "document.d.ts", "document.d.cts", "document_loaders/base.cjs", "document_loaders/base.js", "document_loaders/base.d.ts", "document_loaders/base.d.cts", "document_loaders/web/apify_dataset.cjs", "document_loaders/web/apify_dataset.js", "document_loaders/web/apify_dataset.d.ts", "document_loaders/web/apify_dataset.d.cts", "document_loaders/web/assemblyai.cjs", "document_loaders/web/assemblyai.js", "document_loaders/web/assemblyai.d.ts", "document_loaders/web/assemblyai.d.cts", "document_loaders/web/azure_blob_storage_container.cjs", "document_loaders/web/azure_blob_storage_container.js", "document_loaders/web/azure_blob_storage_container.d.ts", "document_loaders/web/azure_blob_storage_container.d.cts", "document_loaders/web/azure_blob_storage_file.cjs", "document_loaders/web/azure_blob_storage_file.js", "document_loaders/web/azure_blob_storage_file.d.ts", "document_loaders/web/azure_blob_storage_file.d.cts", "document_loaders/web/browserbase.cjs", "document_loaders/web/browserbase.js", "document_loaders/web/browserbase.d.ts", "document_loaders/web/browserbase.d.cts", "document_loaders/web/cheerio.cjs", "document_loaders/web/cheerio.js", "document_loaders/web/cheerio.d.ts", "document_loaders/web/cheerio.d.cts", "document_loaders/web/puppeteer.cjs", "document_loaders/web/puppeteer.js", "document_loaders/web/puppeteer.d.ts", "document_loaders/web/puppeteer.d.cts", "document_loaders/web/playwright.cjs", "document_loaders/web/playwright.js", "document_loaders/web/playwright.d.ts", "document_loaders/web/playwright.d.cts", "document_loaders/web/college_confidential.cjs", "document_loaders/web/college_confidential.js", "document_loaders/web/college_confidential.d.ts", "document_loaders/web/college_confidential.d.cts", "document_loaders/web/gitbook.cjs", "document_loaders/web/gitbook.js", "document_loaders/web/gitbook.d.ts", "document_loaders/web/gitbook.d.cts", "document_loaders/web/hn.cjs", "document_loaders/web/hn.js", "document_loaders/web/hn.d.ts", "document_loaders/web/hn.d.cts", "document_loaders/web/imsdb.cjs", "document_loaders/web/imsdb.js", "document_loaders/web/imsdb.d.ts", "document_loaders/web/imsdb.d.cts", "document_loaders/web/figma.cjs", "document_loaders/web/figma.js", "document_loaders/web/figma.d.ts", "document_loaders/web/figma.d.cts", "document_loaders/web/firecrawl.cjs", "document_loaders/web/firecrawl.js", "document_loaders/web/firecrawl.d.ts", "document_loaders/web/firecrawl.d.cts", "document_loaders/web/github.cjs", "document_loaders/web/github.js", "document_loaders/web/github.d.ts", "document_loaders/web/github.d.cts", "document_loaders/web/notiondb.cjs", "document_loaders/web/notiondb.js", "document_loaders/web/notiondb.d.ts", "document_loaders/web/notiondb.d.cts", "document_loaders/web/notionapi.cjs", "document_loaders/web/notionapi.js", "document_loaders/web/notionapi.d.ts", "document_loaders/web/notionapi.d.cts", "document_loaders/web/pdf.cjs", "document_loaders/web/pdf.js", "document_loaders/web/pdf.d.ts", "document_loaders/web/pdf.d.cts", "document_loaders/web/recursive_url.cjs", "document_loaders/web/recursive_url.js", "document_loaders/web/recursive_url.d.ts", "document_loaders/web/recursive_url.d.cts", "document_loaders/web/s3.cjs", "document_loaders/web/s3.js", "document_loaders/web/s3.d.ts", "document_loaders/web/s3.d.cts", "document_loaders/web/sitemap.cjs", "document_loaders/web/sitemap.js", "document_loaders/web/sitemap.d.ts", "document_loaders/web/sitemap.d.cts", "document_loaders/web/sonix_audio.cjs", "document_loaders/web/sonix_audio.js", "document_loaders/web/sonix_audio.d.ts", "document_loaders/web/sonix_audio.d.cts", "document_loaders/web/confluence.cjs", "document_loaders/web/confluence.js", "document_loaders/web/confluence.d.ts", "document_loaders/web/confluence.d.cts", "document_loaders/web/couchbase.cjs", "document_loaders/web/couchbase.js", "document_loaders/web/couchbase.d.ts", "document_loaders/web/couchbase.d.cts", "document_loaders/web/searchapi.cjs", "document_loaders/web/searchapi.js", "document_loaders/web/searchapi.d.ts", "document_loaders/web/searchapi.d.cts", "document_loaders/web/serpapi.cjs", "document_loaders/web/serpapi.js", "document_loaders/web/serpapi.d.ts", "document_loaders/web/serpapi.d.cts", "document_loaders/web/sort_xyz_blockchain.cjs", "document_loaders/web/sort_xyz_blockchain.js", "document_loaders/web/sort_xyz_blockchain.d.ts", "document_loaders/web/sort_xyz_blockchain.d.cts", "document_loaders/web/youtube.cjs", "document_loaders/web/youtube.js", "document_loaders/web/youtube.d.ts", "document_loaders/web/youtube.d.cts", "document_loaders/fs/directory.cjs", "document_loaders/fs/directory.js", "document_loaders/fs/directory.d.ts", "document_loaders/fs/directory.d.cts", "document_loaders/fs/buffer.cjs", "document_loaders/fs/buffer.js", "document_loaders/fs/buffer.d.ts", "document_loaders/fs/buffer.d.cts", "document_loaders/fs/chatgpt.cjs", "document_loaders/fs/chatgpt.js", "document_loaders/fs/chatgpt.d.ts", "document_loaders/fs/chatgpt.d.cts", "document_loaders/fs/text.cjs", "document_loaders/fs/text.js", "document_loaders/fs/text.d.ts", "document_loaders/fs/text.d.cts", "document_loaders/fs/json.cjs", "document_loaders/fs/json.js", "document_loaders/fs/json.d.ts", "document_loaders/fs/json.d.cts", "document_loaders/fs/srt.cjs", "document_loaders/fs/srt.js", "document_loaders/fs/srt.d.ts", "document_loaders/fs/srt.d.cts", "document_loaders/fs/pdf.cjs", "document_loaders/fs/pdf.js", "document_loaders/fs/pdf.d.ts", "document_loaders/fs/pdf.d.cts", "document_loaders/fs/docx.cjs", "document_loaders/fs/docx.js", "document_loaders/fs/docx.d.ts", "document_loaders/fs/docx.d.cts", "document_loaders/fs/epub.cjs", "document_loaders/fs/epub.js", "document_loaders/fs/epub.d.ts", "document_loaders/fs/epub.d.cts", "document_loaders/fs/csv.cjs", "document_loaders/fs/csv.js", "document_loaders/fs/csv.d.ts", "document_loaders/fs/csv.d.cts", "document_loaders/fs/notion.cjs", "document_loaders/fs/notion.js", "document_loaders/fs/notion.d.ts", "document_loaders/fs/notion.d.cts", "document_loaders/fs/obsidian.cjs", "document_loaders/fs/obsidian.js", "document_loaders/fs/obsidian.d.ts", "document_loaders/fs/obsidian.d.cts", "document_loaders/fs/unstructured.cjs", "document_loaders/fs/unstructured.js", "document_loaders/fs/unstructured.d.ts", "document_loaders/fs/unstructured.d.cts", "document_loaders/fs/openai_whisper_audio.cjs", "document_loaders/fs/openai_whisper_audio.js", "document_loaders/fs/openai_whisper_audio.d.ts", "document_loaders/fs/openai_whisper_audio.d.cts", "document_loaders/fs/pptx.cjs", "document_loaders/fs/pptx.js", "document_loaders/fs/pptx.d.ts", "document_loaders/fs/pptx.d.cts", "document_transformers/html_to_text.cjs", "document_transformers/html_to_text.js", "document_transformers/html_to_text.d.ts", "document_transformers/html_to_text.d.cts", "document_transformers/mozilla_readability.cjs", "document_transformers/mozilla_readability.js", "document_transformers/mozilla_readability.d.ts", "document_transformers/mozilla_readability.d.cts", "document_transformers/openai_functions.cjs", "document_transformers/openai_functions.js", "document_transformers/openai_functions.d.ts", "document_transformers/openai_functions.d.cts", "chat_models/base.cjs", "chat_models/base.js", "chat_models/base.d.ts", "chat_models/base.d.cts", "chat_models/openai.cjs", "chat_models/openai.js", "chat_models/openai.d.ts", "chat_models/openai.d.cts", "chat_models/portkey.cjs", "chat_models/portkey.js", "chat_models/portkey.d.ts", "chat_models/portkey.d.cts", "chat_models/anthropic.cjs", "chat_models/anthropic.js", "chat_models/anthropic.d.ts", "chat_models/anthropic.d.cts", "chat_models/bedrock.cjs", "chat_models/bedrock.js", "chat_models/bedrock.d.ts", "chat_models/bedrock.d.cts", "chat_models/bedrock/web.cjs", "chat_models/bedrock/web.js", "chat_models/bedrock/web.d.ts", "chat_models/bedrock/web.d.cts", "chat_models/cloudflare_workersai.cjs", "chat_models/cloudflare_workersai.js", "chat_models/cloudflare_workersai.d.ts", "chat_models/cloudflare_workersai.d.cts", "chat_models/googlevertexai.cjs", "chat_models/googlevertexai.js", "chat_models/googlevertexai.d.ts", "chat_models/googlevertexai.d.cts", "chat_models/googlevertexai/web.cjs", "chat_models/googlevertexai/web.js", "chat_models/googlevertexai/web.d.ts", "chat_models/googlevertexai/web.d.cts", "chat_models/googlepalm.cjs", "chat_models/googlepalm.js", "chat_models/googlepalm.d.ts", "chat_models/googlepalm.d.cts", "chat_models/fireworks.cjs", "chat_models/fireworks.js", "chat_models/fireworks.d.ts", "chat_models/fireworks.d.cts", "chat_models/baiduwenxin.cjs", "chat_models/baiduwenxin.js", "chat_models/baiduwenxin.d.ts", "chat_models/baiduwenxin.d.cts", "chat_models/iflytek_xinghuo.cjs", "chat_models/iflytek_xinghuo.js", "chat_models/iflytek_xinghuo.d.ts", "chat_models/iflytek_xinghuo.d.cts", "chat_models/iflytek_xinghuo/web.cjs", "chat_models/iflytek_xinghuo/web.js", "chat_models/iflytek_xinghuo/web.d.ts", "chat_models/iflytek_xinghuo/web.d.cts", "chat_models/ollama.cjs", "chat_models/ollama.js", "chat_models/ollama.d.ts", "chat_models/ollama.d.cts", "chat_models/minimax.cjs", "chat_models/minimax.js", "chat_models/minimax.d.ts", "chat_models/minimax.d.cts", "chat_models/llama_cpp.cjs", "chat_models/llama_cpp.js", "chat_models/llama_cpp.d.ts", "chat_models/llama_cpp.d.cts", "chat_models/yandex.cjs", "chat_models/yandex.js", "chat_models/yandex.d.ts", "chat_models/yandex.d.cts", "chat_models/fake.cjs", "chat_models/fake.js", "chat_models/fake.d.ts", "chat_models/fake.d.cts", "schema.cjs", "schema.js", "schema.d.ts", "schema.d.cts", "schema/document.cjs", "schema/document.js", "schema/document.d.ts", "schema/document.d.cts", "schema/output_parser.cjs", "schema/output_parser.js", "schema/output_parser.d.ts", "schema/output_parser.d.cts", "schema/prompt_template.cjs", "schema/prompt_template.js", "schema/prompt_template.d.ts", "schema/prompt_template.d.cts", "schema/query_constructor.cjs", "schema/query_constructor.js", "schema/query_constructor.d.ts", "schema/query_constructor.d.cts", "schema/retriever.cjs", "schema/retriever.js", "schema/retriever.d.ts", "schema/retriever.d.cts", "schema/runnable.cjs", "schema/runnable.js", "schema/runnable.d.ts", "schema/runnable.d.cts", "schema/storage.cjs", "schema/storage.js", "schema/storage.d.ts", "schema/storage.d.cts", "sql_db.cjs", "sql_db.js", "sql_db.d.ts", "sql_db.d.cts", "callbacks.cjs", "callbacks.js", "callbacks.d.ts", "callbacks.d.cts", "callbacks/handlers/llmonitor.cjs", "callbacks/handlers/llmonitor.js", "callbacks/handlers/llmonitor.d.ts", "callbacks/handlers/llmonitor.d.cts", "output_parsers.cjs", "output_parsers.js", "output_parsers.d.ts", "output_parsers.d.cts", "output_parsers/expression.cjs", "output_parsers/expression.js", "output_parsers/expression.d.ts", "output_parsers/expression.d.cts", "retrievers/amazon_kendra.cjs", "retrievers/amazon_kendra.js", "retrievers/amazon_kendra.d.ts", "retrievers/amazon_kendra.d.cts", "retrievers/remote.cjs", "retrievers/remote.js", "retrievers/remote.d.ts", "retrievers/remote.d.cts", "retrievers/supabase.cjs", "retrievers/supabase.js", "retrievers/supabase.d.ts", "retrievers/supabase.d.cts", "retrievers/zep.cjs", "retrievers/zep.js", "retrievers/zep.d.ts", "retrievers/zep.d.cts", "retrievers/metal.cjs", "retrievers/metal.js", "retrievers/metal.d.ts", "retrievers/metal.d.cts", "retrievers/chaindesk.cjs", "retrievers/chaindesk.js", "retrievers/chaindesk.d.ts", "retrievers/chaindesk.d.cts", "retrievers/databerry.cjs", "retrievers/databerry.js", "retrievers/databerry.d.ts", "retrievers/databerry.d.cts", "retrievers/contextual_compression.cjs", "retrievers/contextual_compression.js", "retrievers/contextual_compression.d.ts", "retrievers/contextual_compression.d.cts", "retrievers/document_compressors.cjs", "retrievers/document_compressors.js", "retrievers/document_compressors.d.ts", "retrievers/document_compressors.d.cts", "retrievers/multi_query.cjs", "retrievers/multi_query.js", "retrievers/multi_query.d.ts", "retrievers/multi_query.d.cts", "retrievers/multi_vector.cjs", "retrievers/multi_vector.js", "retrievers/multi_vector.d.ts", "retrievers/multi_vector.d.cts", "retrievers/parent_document.cjs", "retrievers/parent_document.js", "retrievers/parent_document.d.ts", "retrievers/parent_document.d.cts", "retrievers/vectara_summary.cjs", "retrievers/vectara_summary.js", "retrievers/vectara_summary.d.ts", "retrievers/vectara_summary.d.cts", "retrievers/tavily_search_api.cjs", "retrievers/tavily_search_api.js", "retrievers/tavily_search_api.d.ts", "retrievers/tavily_search_api.d.cts", "retrievers/time_weighted.cjs", "retrievers/time_weighted.js", "retrievers/time_weighted.d.ts", "retrievers/time_weighted.d.cts", "retrievers/document_compressors/chain_extract.cjs", "retrievers/document_compressors/chain_extract.js", "retrievers/document_compressors/chain_extract.d.ts", "retrievers/document_compressors/chain_extract.d.cts", "retrievers/document_compressors/embeddings_filter.cjs", "retrievers/document_compressors/embeddings_filter.js", "retrievers/document_compressors/embeddings_filter.d.ts", "retrievers/document_compressors/embeddings_filter.d.cts", "retrievers/hyde.cjs", "retrievers/hyde.js", "retrievers/hyde.d.ts", "retrievers/hyde.d.cts", "retrievers/score_threshold.cjs", "retrievers/score_threshold.js", "retrievers/score_threshold.d.ts", "retrievers/score_threshold.d.cts", "retrievers/self_query.cjs", "retrievers/self_query.js", "retrievers/self_query.d.ts", "retrievers/self_query.d.cts", "retrievers/self_query/chroma.cjs", "retrievers/self_query/chroma.js", "retrievers/self_query/chroma.d.ts", "retrievers/self_query/chroma.d.cts", "retrievers/self_query/functional.cjs", "retrievers/self_query/functional.js", "retrievers/self_query/functional.d.ts", "retrievers/self_query/functional.d.cts", "retrievers/self_query/pinecone.cjs", "retrievers/self_query/pinecone.js", "retrievers/self_query/pinecone.d.ts", "retrievers/self_query/pinecone.d.cts", "retrievers/self_query/supabase.cjs", "retrievers/self_query/supabase.js", "retrievers/self_query/supabase.d.ts", "retrievers/self_query/supabase.d.cts", "retrievers/self_query/weaviate.cjs", "retrievers/self_query/weaviate.js", "retrievers/self_query/weaviate.d.ts", "retrievers/self_query/weaviate.d.cts", "retrievers/self_query/vectara.cjs", "retrievers/self_query/vectara.js", "retrievers/self_query/vectara.d.ts", "retrievers/self_query/vectara.d.cts", "retrievers/vespa.cjs", "retrievers/vespa.js", "retrievers/vespa.d.ts", "retrievers/vespa.d.cts", "retrievers/matryoshka_retriever.cjs", "retrievers/matryoshka_retriever.js", "retrievers/matryoshka_retriever.d.ts", "retrievers/matryoshka_retriever.d.cts", "cache.cjs", "cache.js", "cache.d.ts", "cache.d.cts", "cache/cloudflare_kv.cjs", "cache/cloudflare_kv.js", "cache/cloudflare_kv.d.ts", "cache/cloudflare_kv.d.cts", "cache/momento.cjs", "cache/momento.js", "cache/momento.d.ts", "cache/momento.d.cts", "cache/redis.cjs", "cache/redis.js", "cache/redis.d.ts", "cache/redis.d.cts", "cache/ioredis.cjs", "cache/ioredis.js", "cache/ioredis.d.ts", "cache/ioredis.d.cts", "cache/file_system.cjs", "cache/file_system.js", "cache/file_system.d.ts", "cache/file_system.d.cts", "cache/upstash_redis.cjs", "cache/upstash_redis.js", "cache/upstash_redis.d.ts", "cache/upstash_redis.d.cts", "stores/doc/in_memory.cjs", "stores/doc/in_memory.js", "stores/doc/in_memory.d.ts", "stores/doc/in_memory.d.cts", "stores/doc/gcs.cjs", "stores/doc/gcs.js", "stores/doc/gcs.d.ts", "stores/doc/gcs.d.cts", "stores/file/in_memory.cjs", "stores/file/in_memory.js", "stores/file/in_memory.d.ts", "stores/file/in_memory.d.cts", "stores/file/node.cjs", "stores/file/node.js", "stores/file/node.d.ts", "stores/file/node.d.cts", "stores/message/cassandra.cjs", "stores/message/cassandra.js", "stores/message/cassandra.d.ts", "stores/message/cassandra.d.cts", "stores/message/convex.cjs", "stores/message/convex.js", "stores/message/convex.d.ts", "stores/message/convex.d.cts", "stores/message/cloudflare_d1.cjs", "stores/message/cloudflare_d1.js", "stores/message/cloudflare_d1.d.ts", "stores/message/cloudflare_d1.d.cts", "stores/message/in_memory.cjs", "stores/message/in_memory.js", "stores/message/in_memory.d.ts", "stores/message/in_memory.d.cts", "stores/message/dynamodb.cjs", "stores/message/dynamodb.js", "stores/message/dynamodb.d.ts", "stores/message/dynamodb.d.cts", "stores/message/firestore.cjs", "stores/message/firestore.js", "stores/message/firestore.d.ts", "stores/message/firestore.d.cts", "stores/message/momento.cjs", "stores/message/momento.js", "stores/message/momento.d.ts", "stores/message/momento.d.cts", "stores/message/mongodb.cjs", "stores/message/mongodb.js", "stores/message/mongodb.d.ts", "stores/message/mongodb.d.cts", "stores/message/redis.cjs", "stores/message/redis.js", "stores/message/redis.d.ts", "stores/message/redis.d.cts", "stores/message/ioredis.cjs", "stores/message/ioredis.js", "stores/message/ioredis.d.ts", "stores/message/ioredis.d.cts", "stores/message/upstash_redis.cjs", "stores/message/upstash_redis.js", "stores/message/upstash_redis.d.ts", "stores/message/upstash_redis.d.cts", "stores/message/planetscale.cjs", "stores/message/planetscale.js", "stores/message/planetscale.d.ts", "stores/message/planetscale.d.cts", "stores/message/xata.cjs", "stores/message/xata.js", "stores/message/xata.d.ts", "stores/message/xata.d.cts", "storage/convex.cjs", "storage/convex.js", "storage/convex.d.ts", "storage/convex.d.cts", "storage/encoder_backed.cjs", "storage/encoder_backed.js", "storage/encoder_backed.d.ts", "storage/encoder_backed.d.cts", "storage/in_memory.cjs", "storage/in_memory.js", "storage/in_memory.d.ts", "storage/in_memory.d.cts", "storage/ioredis.cjs", "storage/ioredis.js", "storage/ioredis.d.ts", "storage/ioredis.d.cts", "storage/vercel_kv.cjs", "storage/vercel_kv.js", "storage/vercel_kv.d.ts", "storage/vercel_kv.d.cts", "storage/upstash_redis.cjs", "storage/upstash_redis.js", "storage/upstash_redis.d.ts", "storage/upstash_redis.d.cts", "storage/file_system.cjs", "storage/file_system.js", "storage/file_system.d.ts", "storage/file_system.d.cts", "graphs/neo4j_graph.cjs", "graphs/neo4j_graph.js", "graphs/neo4j_graph.d.ts", "graphs/neo4j_graph.d.cts", "hub.cjs", "hub.js", "hub.d.ts", "hub.d.cts", "util/convex.cjs", "util/convex.js", "util/convex.d.ts", "util/convex.d.cts", "util/document.cjs", "util/document.js", "util/document.d.ts", "util/document.d.cts", "util/math.cjs", "util/math.js", "util/math.d.ts", "util/math.d.cts", "util/time.cjs", "util/time.js", "util/time.d.ts", "util/time.d.cts", "experimental/autogpt.cjs", "experimental/autogpt.js", "experimental/autogpt.d.ts", "experimental/autogpt.d.cts", "experimental/openai_assistant.cjs", "experimental/openai_assistant.js", "experimental/openai_assistant.d.ts", "experimental/openai_assistant.d.cts", "experimental/openai_files.cjs", "experimental/openai_files.js", "experimental/openai_files.d.ts", "experimental/openai_files.d.cts", "experimental/babyagi.cjs", "experimental/babyagi.js", "experimental/babyagi.d.ts", "experimental/babyagi.d.cts", "experimental/generative_agents.cjs", "experimental/generative_agents.js", "experimental/generative_agents.d.ts", "experimental/generative_agents.d.cts", "experimental/plan_and_execute.cjs", "experimental/plan_and_execute.js", "experimental/plan_and_execute.d.ts", "experimental/plan_and_execute.d.cts", "experimental/multimodal_embeddings/googlevertexai.cjs", "experimental/multimodal_embeddings/googlevertexai.js", "experimental/multimodal_embeddings/googlevertexai.d.ts", "experimental/multimodal_embeddings/googlevertexai.d.cts", "experimental/chat_models/anthropic_functions.cjs", "experimental/chat_models/anthropic_functions.js", "experimental/chat_models/anthropic_functions.d.ts", "experimental/chat_models/anthropic_functions.d.cts", "experimental/chat_models/bittensor.cjs", "experimental/chat_models/bittensor.js", "experimental/chat_models/bittensor.d.ts", "experimental/chat_models/bittensor.d.cts", "experimental/chat_models/ollama_functions.cjs", "experimental/chat_models/ollama_functions.js", "experimental/chat_models/ollama_functions.d.ts", "experimental/chat_models/ollama_functions.d.cts", "experimental/llms/bittensor.cjs", "experimental/llms/bittensor.js", "experimental/llms/bittensor.d.ts", "experimental/llms/bittensor.d.cts", "experimental/hubs/makersuite/googlemakersuitehub.cjs", "experimental/hubs/makersuite/googlemakersuitehub.js", "experimental/hubs/makersuite/googlemakersuitehub.d.ts", "experimental/hubs/makersuite/googlemakersuitehub.d.cts", "experimental/chains/violation_of_expectations.cjs", "experimental/chains/violation_of_expectations.js", "experimental/chains/violation_of_expectations.d.ts", "experimental/chains/violation_of_expectations.d.cts", "experimental/masking.cjs", "experimental/masking.js", "experimental/masking.d.ts", "experimental/masking.d.cts", "experimental/prompts/custom_format.cjs", "experimental/prompts/custom_format.js", "experimental/prompts/custom_format.d.ts", "experimental/prompts/custom_format.d.cts", "experimental/prompts/handlebars.cjs", "experimental/prompts/handlebars.js", "experimental/prompts/handlebars.d.ts", "experimental/prompts/handlebars.d.cts", "experimental/tools/pyinterpreter.cjs", "experimental/tools/pyinterpreter.js", "experimental/tools/pyinterpreter.d.ts", "experimental/tools/pyinterpreter.d.cts", "evaluation.cjs", "evaluation.js", "evaluation.d.ts", "evaluation.d.cts", "smith.cjs", "smith.js", "smith.d.ts", "smith.d.cts", "runnables.cjs", "runnables.js", "runnables.d.ts", "runnables.d.cts", "runnables/remote.cjs", "runnables/remote.js", "runnables/remote.d.ts", "runnables/remote.d.cts", "indexes.cjs", "indexes.js", "indexes.d.ts", "indexes.d.cts"], "repository": {"type": "git", "url": "**************:langchain-ai/langchainjs.git"}, "homepage": "https://github.com/langchain-ai/langchainjs/tree/main/langchain/", "scripts": {"build": "yarn run build:deps && yarn clean && yarn build:esm && yarn build:cjs && yarn build:scripts", "build:deps": "yarn run turbo:command build --filter=@langchain/openai --filter=@langchain/community --filter=@langchain/textsplitters --concurrency=1", "build:esm": "NODE_OPTIONS=--max-old-space-size=4096 tsc --outDir dist/ && rimraf dist/tests dist/**/tests", "build:cjs": "NODE_OPTIONS=--max-old-space-size=4096 tsc --outDir dist-cjs/ -p tsconfig.cjs.json && yarn move-cjs-to-dist && rimraf dist-cjs", "build:watch": "yarn create-entrypoints && tsc --outDir dist/ --watch", "build:scripts": "yarn create-entrypoints && yarn check-tree-shaking", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint:dpdm": "dpdm --exit-code circular:1 --no-warning --no-tree src/*.ts src/**/*.ts", "lint": "yarn lint:eslint && yarn lint:dpdm", "lint:fix": "yarn lint:eslint --fix && yarn lint:dpdm", "precommit": "lint-staged", "clean": "rimraf .turbo/ dist/ && NODE_OPTIONS=--max-old-space-size=4096 yarn lc-build --config ./langchain.config.js --create-entrypoints --pre --gen-maps", "prepack": "yarn build", "release": "release-it --only-version --config .release-it.json", "test": "yarn run build:deps && NODE_OPTIONS=--experimental-vm-modules jest --testPathIgnorePatterns=\\.int\\.test.ts --testTimeout 30000 --maxWorkers=50%", "test:watch": "yarn run build:deps && NODE_OPTIONS=--experimental-vm-modules jest --watch --testPathIgnorePatterns=\\.int\\.test.ts", "test:integration": "yarn run build:deps && NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:single": "yarn run build:deps && NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.cjs --testTimeout 100000", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\"", "move-cjs-to-dist": "yarn lc-build --config ./langchain.config.js --move-cjs-dist", "create-entrypoints": "yarn lc-build --config ./langchain.config.js --create-entrypoints", "check-tree-shaking": "yarn lc-build --config ./langchain.config.js --tree-shaking"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@aws-sdk/client-s3": "^3.310.0", "@aws-sdk/client-sagemaker-runtime": "^3.414.0", "@aws-sdk/client-sfn": "^3.362.0", "@aws-sdk/credential-provider-node": "^3.388.0", "@aws-sdk/types": "^3.357.0", "@azure/storage-blob": "^12.15.0", "@browserbasehq/sdk": "^1.0.0", "@cloudflare/workers-types": "^4.20230922.0", "@faker-js/faker": "^7.6.0", "@gomomento/sdk": "^1.51.1", "@gomomento/sdk-core": "^1.51.1", "@google-ai/generativelanguage": "^0.2.1", "@google-cloud/storage": "^7.7.0", "@jest/globals": "^29.5.0", "@langchain/scripts": "~0.0", "@mendable/firecrawl-js": "^0.0.13", "@notionhq/client": "^2.2.10", "@pinecone-database/pinecone": "^1.1.0", "@supabase/supabase-js": "^2.10.0", "@swc/core": "^1.3.90", "@swc/jest": "^0.2.29", "@tsconfig/recommended": "^1.0.2", "@types/d3-dsv": "^2", "@types/decamelize": "^1.2.0", "@types/handlebars": "^4.1.0", "@types/html-to-text": "^9", "@types/js-yaml": "^4", "@types/jsdom": "^21.1.1", "@types/pdf-parse": "^1.1.1", "@types/uuid": "^9", "@types/ws": "^8", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vercel/kv": "^0.2.3", "@xata.io/client": "^0.28.0", "apify-client": "^2.7.1", "assemblyai": "^4.0.0", "axios": "^0.26.0", "cheerio": "^1.0.0-rc.12", "chromadb": "^1.5.3", "convex": "^1.3.1", "couchbase": "^4.3.0", "d3-dsv": "^2.0.0", "dotenv": "^16.0.3", "dpdm": "^3.12.0", "epub2": "^3.0.1", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "fast-xml-parser": "^4.2.7", "google-auth-library": "^8.9.0", "handlebars": "^4.7.8", "html-to-text": "^9.0.5", "ignore": "^5.2.0", "ioredis": "^5.3.2", "jest": "^29.5.0", "jest-environment-node": "^29.6.4", "jsdom": "^22.1.0", "mammoth": "^1.5.1", "mongodb": "^5.2.0", "node-llama-cpp": "2.7.3", "notion-to-md": "^3.1.0", "officeparser": "^4.0.4", "openai": "^4.32.1", "pdf-parse": "1.1.1", "peggy": "^3.0.2", "playwright": "^1.32.1", "prettier": "^2.8.3", "puppeteer": "^19.7.2", "pyodide": "^0.24.1", "redis": "^4.6.6", "release-it": "^15.10.1", "rimraf": "^5.0.1", "rollup": "^3.19.1", "sonix-speech-recognition": "^2.1.1", "srt-parser-2": "^1.2.3", "ts-jest": "^29.1.0", "typeorm": "^0.3.12", "typescript": "~5.1.6", "weaviate-ts-client": "^2.0.0", "web-auth-library": "^1.0.3", "wikipedia": "^2.1.2", "youtube-transcript": "^1.0.6", "youtubei.js": "^9.1.0"}, "peerDependencies": {"@aws-sdk/client-s3": "^3.310.0", "@aws-sdk/client-sagemaker-runtime": "^3.310.0", "@aws-sdk/client-sfn": "^3.310.0", "@aws-sdk/credential-provider-node": "^3.388.0", "@azure/storage-blob": "^12.15.0", "@browserbasehq/sdk": "*", "@gomomento/sdk": "^1.51.1", "@gomomento/sdk-core": "^1.51.1", "@gomomento/sdk-web": "^1.51.1", "@google-ai/generativelanguage": "^0.2.1", "@google-cloud/storage": "^6.10.1 || ^7.7.0", "@mendable/firecrawl-js": "^0.0.13", "@notionhq/client": "^2.2.10", "@pinecone-database/pinecone": "*", "@supabase/supabase-js": "^2.10.0", "@vercel/kv": "^0.2.3", "@xata.io/client": "^0.28.0", "apify-client": "^2.7.1", "assemblyai": "^4.0.0", "axios": "*", "cheerio": "^1.0.0-rc.12", "chromadb": "*", "convex": "^1.3.1", "couchbase": "^4.3.0", "d3-dsv": "^2.0.0", "epub2": "^3.0.1", "fast-xml-parser": "*", "google-auth-library": "^8.9.0", "handlebars": "^4.7.8", "html-to-text": "^9.0.5", "ignore": "^5.2.0", "ioredis": "^5.3.2", "jsdom": "*", "mammoth": "^1.6.0", "mongodb": ">=5.2.0", "node-llama-cpp": "*", "notion-to-md": "^3.1.0", "officeparser": "^4.0.4", "pdf-parse": "1.1.1", "peggy": "^3.0.2", "playwright": "^1.32.1", "puppeteer": "^19.7.2", "pyodide": "^0.24.1", "redis": "^4.6.4", "sonix-speech-recognition": "^2.1.1", "srt-parser-2": "^1.2.3", "typeorm": "^0.3.12", "weaviate-ts-client": "*", "web-auth-library": "^1.0.3", "ws": "^8.14.2", "youtube-transcript": "^1.0.6", "youtubei.js": "^9.1.0"}, "peerDependenciesMeta": {"@aws-sdk/client-s3": {"optional": true}, "@aws-sdk/client-sagemaker-runtime": {"optional": true}, "@aws-sdk/client-sfn": {"optional": true}, "@aws-sdk/credential-provider-node": {"optional": true}, "@azure/storage-blob": {"optional": true}, "@browserbasehq/sdk": {"optional": true}, "@gomomento/sdk": {"optional": true}, "@gomomento/sdk-core": {"optional": true}, "@gomomento/sdk-web": {"optional": true}, "@google-ai/generativelanguage": {"optional": true}, "@google-cloud/storage": {"optional": true}, "@mendable/firecrawl-js": {"optional": true}, "@notionhq/client": {"optional": true}, "@pinecone-database/pinecone": {"optional": true}, "@supabase/supabase-js": {"optional": true}, "@vercel/kv": {"optional": true}, "@xata.io/client": {"optional": true}, "apify-client": {"optional": true}, "assemblyai": {"optional": true}, "axios": {"optional": true}, "cheerio": {"optional": true}, "chromadb": {"optional": true}, "convex": {"optional": true}, "couchbase": {"optional": true}, "d3-dsv": {"optional": true}, "epub2": {"optional": true}, "faiss-node": {"optional": true}, "fast-xml-parser": {"optional": true}, "google-auth-library": {"optional": true}, "handlebars": {"optional": true}, "html-to-text": {"optional": true}, "ignore": {"optional": true}, "ioredis": {"optional": true}, "jsdom": {"optional": true}, "mammoth": {"optional": true}, "mongodb": {"optional": true}, "node-llama-cpp": {"optional": true}, "notion-to-md": {"optional": true}, "officeparser": {"optional": true}, "pdf-parse": {"optional": true}, "peggy": {"optional": true}, "playwright": {"optional": true}, "puppeteer": {"optional": true}, "pyodide": {"optional": true}, "redis": {"optional": true}, "sonix-speech-recognition": {"optional": true}, "srt-parser-2": {"optional": true}, "typeorm": {"optional": true}, "weaviate-ts-client": {"optional": true}, "web-auth-library": {"optional": true}, "ws": {"optional": true}, "youtube-transcript": {"optional": true}, "youtubei.js": {"optional": true}}, "dependencies": {"@anthropic-ai/sdk": "^0.9.1", "@langchain/community": "~0.0.47", "@langchain/core": "~0.1.60", "@langchain/openai": "~0.0.28", "@langchain/textsplitters": "~0.0.0", "binary-extensions": "^2.2.0", "js-tiktoken": "^1.0.7", "js-yaml": "^4.1.0", "jsonpointer": "^5.0.1", "langchainhub": "~0.0.8", "langsmith": "~0.1.7", "ml-distance": "^4.0.0", "openapi-types": "^12.1.3", "p-retry": "4", "uuid": "^9.0.0", "yaml": "^2.2.1", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.3"}, "publishConfig": {"access": "public"}, "keywords": ["llm", "ai", "gpt3", "chain", "prompt", "prompt engineering", "chatgpt", "machine learning", "ml", "openai", "embeddings", "vectorstores"], "exports": {"./load": {"types": {"import": "./load.d.ts", "require": "./load.d.cts", "default": "./load.d.ts"}, "import": "./load.js", "require": "./load.cjs"}, "./load/serializable": {"types": {"import": "./load/serializable.d.ts", "require": "./load/serializable.d.cts", "default": "./load/serializable.d.ts"}, "import": "./load/serializable.js", "require": "./load/serializable.cjs"}, "./agents": {"types": {"import": "./agents.d.ts", "require": "./agents.d.cts", "default": "./agents.d.ts"}, "import": "./agents.js", "require": "./agents.cjs"}, "./agents/load": {"types": {"import": "./agents/load.d.ts", "require": "./agents/load.d.cts", "default": "./agents/load.d.ts"}, "import": "./agents/load.js", "require": "./agents/load.cjs"}, "./agents/toolkits": {"types": {"import": "./agents/toolkits.d.ts", "require": "./agents/toolkits.d.cts", "default": "./agents/toolkits.d.ts"}, "import": "./agents/toolkits.js", "require": "./agents/toolkits.cjs"}, "./agents/toolkits/aws_sfn": {"types": {"import": "./agents/toolkits/aws_sfn.d.ts", "require": "./agents/toolkits/aws_sfn.d.cts", "default": "./agents/toolkits/aws_sfn.d.ts"}, "import": "./agents/toolkits/aws_sfn.js", "require": "./agents/toolkits/aws_sfn.cjs"}, "./agents/toolkits/connery": {"types": {"import": "./agents/toolkits/connery.d.ts", "require": "./agents/toolkits/connery.d.cts", "default": "./agents/toolkits/connery.d.ts"}, "import": "./agents/toolkits/connery.js", "require": "./agents/toolkits/connery.cjs"}, "./agents/toolkits/sql": {"types": {"import": "./agents/toolkits/sql.d.ts", "require": "./agents/toolkits/sql.d.cts", "default": "./agents/toolkits/sql.d.ts"}, "import": "./agents/toolkits/sql.js", "require": "./agents/toolkits/sql.cjs"}, "./agents/format_scratchpad": {"types": {"import": "./agents/format_scratchpad.d.ts", "require": "./agents/format_scratchpad.d.cts", "default": "./agents/format_scratchpad.d.ts"}, "import": "./agents/format_scratchpad.js", "require": "./agents/format_scratchpad.cjs"}, "./agents/format_scratchpad/openai_tools": {"types": {"import": "./agents/format_scratchpad/openai_tools.d.ts", "require": "./agents/format_scratchpad/openai_tools.d.cts", "default": "./agents/format_scratchpad/openai_tools.d.ts"}, "import": "./agents/format_scratchpad/openai_tools.js", "require": "./agents/format_scratchpad/openai_tools.cjs"}, "./agents/format_scratchpad/log": {"types": {"import": "./agents/format_scratchpad/log.d.ts", "require": "./agents/format_scratchpad/log.d.cts", "default": "./agents/format_scratchpad/log.d.ts"}, "import": "./agents/format_scratchpad/log.js", "require": "./agents/format_scratchpad/log.cjs"}, "./agents/format_scratchpad/xml": {"types": {"import": "./agents/format_scratchpad/xml.d.ts", "require": "./agents/format_scratchpad/xml.d.cts", "default": "./agents/format_scratchpad/xml.d.ts"}, "import": "./agents/format_scratchpad/xml.js", "require": "./agents/format_scratchpad/xml.cjs"}, "./agents/format_scratchpad/log_to_message": {"types": {"import": "./agents/format_scratchpad/log_to_message.d.ts", "require": "./agents/format_scratchpad/log_to_message.d.cts", "default": "./agents/format_scratchpad/log_to_message.d.ts"}, "import": "./agents/format_scratchpad/log_to_message.js", "require": "./agents/format_scratchpad/log_to_message.cjs"}, "./agents/react/output_parser": {"types": {"import": "./agents/react/output_parser.d.ts", "require": "./agents/react/output_parser.d.cts", "default": "./agents/react/output_parser.d.ts"}, "import": "./agents/react/output_parser.js", "require": "./agents/react/output_parser.cjs"}, "./agents/xml/output_parser": {"types": {"import": "./agents/xml/output_parser.d.ts", "require": "./agents/xml/output_parser.d.cts", "default": "./agents/xml/output_parser.d.ts"}, "import": "./agents/xml/output_parser.js", "require": "./agents/xml/output_parser.cjs"}, "./agents/openai/output_parser": {"types": {"import": "./agents/openai/output_parser.d.ts", "require": "./agents/openai/output_parser.d.cts", "default": "./agents/openai/output_parser.d.ts"}, "import": "./agents/openai/output_parser.js", "require": "./agents/openai/output_parser.cjs"}, "./base_language": {"types": {"import": "./base_language.d.ts", "require": "./base_language.d.cts", "default": "./base_language.d.ts"}, "import": "./base_language.js", "require": "./base_language.cjs"}, "./tools": {"types": {"import": "./tools.d.ts", "require": "./tools.d.cts", "default": "./tools.d.ts"}, "import": "./tools.js", "require": "./tools.cjs"}, "./tools/aws_lambda": {"types": {"import": "./tools/aws_lambda.d.ts", "require": "./tools/aws_lambda.d.cts", "default": "./tools/aws_lambda.d.ts"}, "import": "./tools/aws_lambda.js", "require": "./tools/aws_lambda.cjs"}, "./tools/aws_sfn": {"types": {"import": "./tools/aws_sfn.d.ts", "require": "./tools/aws_sfn.d.cts", "default": "./tools/aws_sfn.d.ts"}, "import": "./tools/aws_sfn.js", "require": "./tools/aws_sfn.cjs"}, "./tools/calculator": {"types": {"import": "./tools/calculator.d.ts", "require": "./tools/calculator.d.cts", "default": "./tools/calculator.d.ts"}, "import": "./tools/calculator.js", "require": "./tools/calculator.cjs"}, "./tools/chain": {"types": {"import": "./tools/chain.d.ts", "require": "./tools/chain.d.cts", "default": "./tools/chain.d.ts"}, "import": "./tools/chain.js", "require": "./tools/chain.cjs"}, "./tools/connery": {"types": {"import": "./tools/connery.d.ts", "require": "./tools/connery.d.cts", "default": "./tools/connery.d.ts"}, "import": "./tools/connery.js", "require": "./tools/connery.cjs"}, "./tools/render": {"types": {"import": "./tools/render.d.ts", "require": "./tools/render.d.cts", "default": "./tools/render.d.ts"}, "import": "./tools/render.js", "require": "./tools/render.cjs"}, "./tools/retriever": {"types": {"import": "./tools/retriever.d.ts", "require": "./tools/retriever.d.cts", "default": "./tools/retriever.d.ts"}, "import": "./tools/retriever.js", "require": "./tools/retriever.cjs"}, "./tools/sql": {"types": {"import": "./tools/sql.d.ts", "require": "./tools/sql.d.cts", "default": "./tools/sql.d.ts"}, "import": "./tools/sql.js", "require": "./tools/sql.cjs"}, "./tools/webbrowser": {"types": {"import": "./tools/webbrowser.d.ts", "require": "./tools/webbrowser.d.cts", "default": "./tools/webbrowser.d.ts"}, "import": "./tools/webbrowser.js", "require": "./tools/webbrowser.cjs"}, "./tools/gmail": {"types": {"import": "./tools/gmail.d.ts", "require": "./tools/gmail.d.cts", "default": "./tools/gmail.d.ts"}, "import": "./tools/gmail.js", "require": "./tools/gmail.cjs"}, "./tools/google_calendar": {"types": {"import": "./tools/google_calendar.d.ts", "require": "./tools/google_calendar.d.cts", "default": "./tools/google_calendar.d.ts"}, "import": "./tools/google_calendar.js", "require": "./tools/google_calendar.cjs"}, "./tools/google_places": {"types": {"import": "./tools/google_places.d.ts", "require": "./tools/google_places.d.cts", "default": "./tools/google_places.d.ts"}, "import": "./tools/google_places.js", "require": "./tools/google_places.cjs"}, "./chains": {"types": {"import": "./chains.d.ts", "require": "./chains.d.cts", "default": "./chains.d.ts"}, "import": "./chains.js", "require": "./chains.cjs"}, "./chains/combine_documents": {"types": {"import": "./chains/combine_documents.d.ts", "require": "./chains/combine_documents.d.cts", "default": "./chains/combine_documents.d.ts"}, "import": "./chains/combine_documents.js", "require": "./chains/combine_documents.cjs"}, "./chains/combine_documents/reduce": {"types": {"import": "./chains/combine_documents/reduce.d.ts", "require": "./chains/combine_documents/reduce.d.cts", "default": "./chains/combine_documents/reduce.d.ts"}, "import": "./chains/combine_documents/reduce.js", "require": "./chains/combine_documents/reduce.cjs"}, "./chains/history_aware_retriever": {"types": {"import": "./chains/history_aware_retriever.d.ts", "require": "./chains/history_aware_retriever.d.cts", "default": "./chains/history_aware_retriever.d.ts"}, "import": "./chains/history_aware_retriever.js", "require": "./chains/history_aware_retriever.cjs"}, "./chains/load": {"types": {"import": "./chains/load.d.ts", "require": "./chains/load.d.cts", "default": "./chains/load.d.ts"}, "import": "./chains/load.js", "require": "./chains/load.cjs"}, "./chains/openai_functions": {"types": {"import": "./chains/openai_functions.d.ts", "require": "./chains/openai_functions.d.cts", "default": "./chains/openai_functions.d.ts"}, "import": "./chains/openai_functions.js", "require": "./chains/openai_functions.cjs"}, "./chains/query_constructor": {"types": {"import": "./chains/query_constructor.d.ts", "require": "./chains/query_constructor.d.cts", "default": "./chains/query_constructor.d.ts"}, "import": "./chains/query_constructor.js", "require": "./chains/query_constructor.cjs"}, "./chains/query_constructor/ir": {"types": {"import": "./chains/query_constructor/ir.d.ts", "require": "./chains/query_constructor/ir.d.cts", "default": "./chains/query_constructor/ir.d.ts"}, "import": "./chains/query_constructor/ir.js", "require": "./chains/query_constructor/ir.cjs"}, "./chains/retrieval": {"types": {"import": "./chains/retrieval.d.ts", "require": "./chains/retrieval.d.cts", "default": "./chains/retrieval.d.ts"}, "import": "./chains/retrieval.js", "require": "./chains/retrieval.cjs"}, "./chains/sql_db": {"types": {"import": "./chains/sql_db.d.ts", "require": "./chains/sql_db.d.cts", "default": "./chains/sql_db.d.ts"}, "import": "./chains/sql_db.js", "require": "./chains/sql_db.cjs"}, "./chains/graph_qa/cypher": {"types": {"import": "./chains/graph_qa/cypher.d.ts", "require": "./chains/graph_qa/cypher.d.cts", "default": "./chains/graph_qa/cypher.d.ts"}, "import": "./chains/graph_qa/cypher.js", "require": "./chains/graph_qa/cypher.cjs"}, "./embeddings/base": {"types": {"import": "./embeddings/base.d.ts", "require": "./embeddings/base.d.cts", "default": "./embeddings/base.d.ts"}, "import": "./embeddings/base.js", "require": "./embeddings/base.cjs"}, "./embeddings/bedrock": {"types": {"import": "./embeddings/bedrock.d.ts", "require": "./embeddings/bedrock.d.cts", "default": "./embeddings/bedrock.d.ts"}, "import": "./embeddings/bedrock.js", "require": "./embeddings/bedrock.cjs"}, "./embeddings/cache_backed": {"types": {"import": "./embeddings/cache_backed.d.ts", "require": "./embeddings/cache_backed.d.cts", "default": "./embeddings/cache_backed.d.ts"}, "import": "./embeddings/cache_backed.js", "require": "./embeddings/cache_backed.cjs"}, "./embeddings/cloudflare_workersai": {"types": {"import": "./embeddings/cloudflare_workersai.d.ts", "require": "./embeddings/cloudflare_workersai.d.cts", "default": "./embeddings/cloudflare_workersai.d.ts"}, "import": "./embeddings/cloudflare_workersai.js", "require": "./embeddings/cloudflare_workersai.cjs"}, "./embeddings/fake": {"types": {"import": "./embeddings/fake.d.ts", "require": "./embeddings/fake.d.cts", "default": "./embeddings/fake.d.ts"}, "import": "./embeddings/fake.js", "require": "./embeddings/fake.cjs"}, "./embeddings/ollama": {"types": {"import": "./embeddings/ollama.d.ts", "require": "./embeddings/ollama.d.cts", "default": "./embeddings/ollama.d.ts"}, "import": "./embeddings/ollama.js", "require": "./embeddings/ollama.cjs"}, "./embeddings/openai": {"types": {"import": "./embeddings/openai.d.ts", "require": "./embeddings/openai.d.cts", "default": "./embeddings/openai.d.ts"}, "import": "./embeddings/openai.js", "require": "./embeddings/openai.cjs"}, "./embeddings/cohere": {"types": {"import": "./embeddings/cohere.d.ts", "require": "./embeddings/cohere.d.cts", "default": "./embeddings/cohere.d.ts"}, "import": "./embeddings/cohere.js", "require": "./embeddings/cohere.cjs"}, "./embeddings/tensorflow": {"types": {"import": "./embeddings/tensorflow.d.ts", "require": "./embeddings/tensorflow.d.cts", "default": "./embeddings/tensorflow.d.ts"}, "import": "./embeddings/tensorflow.js", "require": "./embeddings/tensorflow.cjs"}, "./embeddings/hf": {"types": {"import": "./embeddings/hf.d.ts", "require": "./embeddings/hf.d.cts", "default": "./embeddings/hf.d.ts"}, "import": "./embeddings/hf.js", "require": "./embeddings/hf.cjs"}, "./embeddings/hf_transformers": {"types": {"import": "./embeddings/hf_transformers.d.ts", "require": "./embeddings/hf_transformers.d.cts", "default": "./embeddings/hf_transformers.d.ts"}, "import": "./embeddings/hf_transformers.js", "require": "./embeddings/hf_transformers.cjs"}, "./embeddings/googlevertexai": {"types": {"import": "./embeddings/googlevertexai.d.ts", "require": "./embeddings/googlevertexai.d.cts", "default": "./embeddings/googlevertexai.d.ts"}, "import": "./embeddings/googlevertexai.js", "require": "./embeddings/googlevertexai.cjs"}, "./embeddings/googlepalm": {"types": {"import": "./embeddings/googlepalm.d.ts", "require": "./embeddings/googlepalm.d.cts", "default": "./embeddings/googlepalm.d.ts"}, "import": "./embeddings/googlepalm.js", "require": "./embeddings/googlepalm.cjs"}, "./embeddings/minimax": {"types": {"import": "./embeddings/minimax.d.ts", "require": "./embeddings/minimax.d.cts", "default": "./embeddings/minimax.d.ts"}, "import": "./embeddings/minimax.js", "require": "./embeddings/minimax.cjs"}, "./embeddings/voyage": {"types": {"import": "./embeddings/voyage.d.ts", "require": "./embeddings/voyage.d.cts", "default": "./embeddings/voyage.d.ts"}, "import": "./embeddings/voyage.js", "require": "./embeddings/voyage.cjs"}, "./embeddings/llama_cpp": {"types": {"import": "./embeddings/llama_cpp.d.ts", "require": "./embeddings/llama_cpp.d.cts", "default": "./embeddings/llama_cpp.d.ts"}, "import": "./embeddings/llama_cpp.js", "require": "./embeddings/llama_cpp.cjs"}, "./embeddings/gradient_ai": {"types": {"import": "./embeddings/gradient_ai.d.ts", "require": "./embeddings/gradient_ai.d.cts", "default": "./embeddings/gradient_ai.d.ts"}, "import": "./embeddings/gradient_ai.js", "require": "./embeddings/gradient_ai.cjs"}, "./llms/load": {"types": {"import": "./llms/load.d.ts", "require": "./llms/load.d.cts", "default": "./llms/load.d.ts"}, "import": "./llms/load.js", "require": "./llms/load.cjs"}, "./llms/base": {"types": {"import": "./llms/base.d.ts", "require": "./llms/base.d.cts", "default": "./llms/base.d.ts"}, "import": "./llms/base.js", "require": "./llms/base.cjs"}, "./llms/openai": {"types": {"import": "./llms/openai.d.ts", "require": "./llms/openai.d.cts", "default": "./llms/openai.d.ts"}, "import": "./llms/openai.js", "require": "./llms/openai.cjs"}, "./llms/ai21": {"types": {"import": "./llms/ai21.d.ts", "require": "./llms/ai21.d.cts", "default": "./llms/ai21.d.ts"}, "import": "./llms/ai21.js", "require": "./llms/ai21.cjs"}, "./llms/aleph_alpha": {"types": {"import": "./llms/aleph_alpha.d.ts", "require": "./llms/aleph_alpha.d.cts", "default": "./llms/aleph_alpha.d.ts"}, "import": "./llms/aleph_alpha.js", "require": "./llms/aleph_alpha.cjs"}, "./llms/cloudflare_workersai": {"types": {"import": "./llms/cloudflare_workersai.d.ts", "require": "./llms/cloudflare_workersai.d.cts", "default": "./llms/cloudflare_workersai.d.ts"}, "import": "./llms/cloudflare_workersai.js", "require": "./llms/cloudflare_workersai.cjs"}, "./llms/cohere": {"types": {"import": "./llms/cohere.d.ts", "require": "./llms/cohere.d.cts", "default": "./llms/cohere.d.ts"}, "import": "./llms/cohere.js", "require": "./llms/cohere.cjs"}, "./llms/hf": {"types": {"import": "./llms/hf.d.ts", "require": "./llms/hf.d.cts", "default": "./llms/hf.d.ts"}, "import": "./llms/hf.js", "require": "./llms/hf.cjs"}, "./llms/raycast": {"types": {"import": "./llms/raycast.d.ts", "require": "./llms/raycast.d.cts", "default": "./llms/raycast.d.ts"}, "import": "./llms/raycast.js", "require": "./llms/raycast.cjs"}, "./llms/ollama": {"types": {"import": "./llms/ollama.d.ts", "require": "./llms/ollama.d.cts", "default": "./llms/ollama.d.ts"}, "import": "./llms/ollama.js", "require": "./llms/ollama.cjs"}, "./llms/replicate": {"types": {"import": "./llms/replicate.d.ts", "require": "./llms/replicate.d.cts", "default": "./llms/replicate.d.ts"}, "import": "./llms/replicate.js", "require": "./llms/replicate.cjs"}, "./llms/fireworks": {"types": {"import": "./llms/fireworks.d.ts", "require": "./llms/fireworks.d.cts", "default": "./llms/fireworks.d.ts"}, "import": "./llms/fireworks.js", "require": "./llms/fireworks.cjs"}, "./llms/googlevertexai": {"types": {"import": "./llms/googlevertexai.d.ts", "require": "./llms/googlevertexai.d.cts", "default": "./llms/googlevertexai.d.ts"}, "import": "./llms/googlevertexai.js", "require": "./llms/googlevertexai.cjs"}, "./llms/googlevertexai/web": {"types": {"import": "./llms/googlevertexai/web.d.ts", "require": "./llms/googlevertexai/web.d.cts", "default": "./llms/googlevertexai/web.d.ts"}, "import": "./llms/googlevertexai/web.js", "require": "./llms/googlevertexai/web.cjs"}, "./llms/googlepalm": {"types": {"import": "./llms/googlepalm.d.ts", "require": "./llms/googlepalm.d.cts", "default": "./llms/googlepalm.d.ts"}, "import": "./llms/googlepalm.js", "require": "./llms/googlepalm.cjs"}, "./llms/gradient_ai": {"types": {"import": "./llms/gradient_ai.d.ts", "require": "./llms/gradient_ai.d.cts", "default": "./llms/gradient_ai.d.ts"}, "import": "./llms/gradient_ai.js", "require": "./llms/gradient_ai.cjs"}, "./llms/sagemaker_endpoint": {"types": {"import": "./llms/sagemaker_endpoint.d.ts", "require": "./llms/sagemaker_endpoint.d.cts", "default": "./llms/sagemaker_endpoint.d.ts"}, "import": "./llms/sagemaker_endpoint.js", "require": "./llms/sagemaker_endpoint.cjs"}, "./llms/watsonx_ai": {"types": {"import": "./llms/watsonx_ai.d.ts", "require": "./llms/watsonx_ai.d.cts", "default": "./llms/watsonx_ai.d.ts"}, "import": "./llms/watsonx_ai.js", "require": "./llms/watsonx_ai.cjs"}, "./llms/bedrock": {"types": {"import": "./llms/bedrock.d.ts", "require": "./llms/bedrock.d.cts", "default": "./llms/bedrock.d.ts"}, "import": "./llms/bedrock.js", "require": "./llms/bedrock.cjs"}, "./llms/bedrock/web": {"types": {"import": "./llms/bedrock/web.d.ts", "require": "./llms/bedrock/web.d.cts", "default": "./llms/bedrock/web.d.ts"}, "import": "./llms/bedrock/web.js", "require": "./llms/bedrock/web.cjs"}, "./llms/llama_cpp": {"types": {"import": "./llms/llama_cpp.d.ts", "require": "./llms/llama_cpp.d.cts", "default": "./llms/llama_cpp.d.ts"}, "import": "./llms/llama_cpp.js", "require": "./llms/llama_cpp.cjs"}, "./llms/writer": {"types": {"import": "./llms/writer.d.ts", "require": "./llms/writer.d.cts", "default": "./llms/writer.d.ts"}, "import": "./llms/writer.js", "require": "./llms/writer.cjs"}, "./llms/portkey": {"types": {"import": "./llms/portkey.d.ts", "require": "./llms/portkey.d.cts", "default": "./llms/portkey.d.ts"}, "import": "./llms/portkey.js", "require": "./llms/portkey.cjs"}, "./llms/yandex": {"types": {"import": "./llms/yandex.d.ts", "require": "./llms/yandex.d.cts", "default": "./llms/yandex.d.ts"}, "import": "./llms/yandex.js", "require": "./llms/yandex.cjs"}, "./llms/fake": {"types": {"import": "./llms/fake.d.ts", "require": "./llms/fake.d.cts", "default": "./llms/fake.d.ts"}, "import": "./llms/fake.js", "require": "./llms/fake.cjs"}, "./prompts": {"types": {"import": "./prompts.d.ts", "require": "./prompts.d.cts", "default": "./prompts.d.ts"}, "import": "./prompts.js", "require": "./prompts.cjs"}, "./prompts/load": {"types": {"import": "./prompts/load.d.ts", "require": "./prompts/load.d.cts", "default": "./prompts/load.d.ts"}, "import": "./prompts/load.js", "require": "./prompts/load.cjs"}, "./vectorstores/clickhouse": {"types": {"import": "./vectorstores/clickhouse.d.ts", "require": "./vectorstores/clickhouse.d.cts", "default": "./vectorstores/clickhouse.d.ts"}, "import": "./vectorstores/clickhouse.js", "require": "./vectorstores/clickhouse.cjs"}, "./vectorstores/analyticdb": {"types": {"import": "./vectorstores/analyticdb.d.ts", "require": "./vectorstores/analyticdb.d.cts", "default": "./vectorstores/analyticdb.d.ts"}, "import": "./vectorstores/analyticdb.js", "require": "./vectorstores/analyticdb.cjs"}, "./vectorstores/base": {"types": {"import": "./vectorstores/base.d.ts", "require": "./vectorstores/base.d.cts", "default": "./vectorstores/base.d.ts"}, "import": "./vectorstores/base.js", "require": "./vectorstores/base.cjs"}, "./vectorstores/cassandra": {"types": {"import": "./vectorstores/cassandra.d.ts", "require": "./vectorstores/cassandra.d.cts", "default": "./vectorstores/cassandra.d.ts"}, "import": "./vectorstores/cassandra.js", "require": "./vectorstores/cassandra.cjs"}, "./vectorstores/convex": {"types": {"import": "./vectorstores/convex.d.ts", "require": "./vectorstores/convex.d.cts", "default": "./vectorstores/convex.d.ts"}, "import": "./vectorstores/convex.js", "require": "./vectorstores/convex.cjs"}, "./vectorstores/elasticsearch": {"types": {"import": "./vectorstores/elasticsearch.d.ts", "require": "./vectorstores/elasticsearch.d.cts", "default": "./vectorstores/elasticsearch.d.ts"}, "import": "./vectorstores/elasticsearch.js", "require": "./vectorstores/elasticsearch.cjs"}, "./vectorstores/memory": {"types": {"import": "./vectorstores/memory.d.ts", "require": "./vectorstores/memory.d.cts", "default": "./vectorstores/memory.d.ts"}, "import": "./vectorstores/memory.js", "require": "./vectorstores/memory.cjs"}, "./vectorstores/cloudflare_vectorize": {"types": {"import": "./vectorstores/cloudflare_vectorize.d.ts", "require": "./vectorstores/cloudflare_vectorize.d.cts", "default": "./vectorstores/cloudflare_vectorize.d.ts"}, "import": "./vectorstores/cloudflare_vectorize.js", "require": "./vectorstores/cloudflare_vectorize.cjs"}, "./vectorstores/closevector/web": {"types": {"import": "./vectorstores/closevector/web.d.ts", "require": "./vectorstores/closevector/web.d.cts", "default": "./vectorstores/closevector/web.d.ts"}, "import": "./vectorstores/closevector/web.js", "require": "./vectorstores/closevector/web.cjs"}, "./vectorstores/closevector/node": {"types": {"import": "./vectorstores/closevector/node.d.ts", "require": "./vectorstores/closevector/node.d.cts", "default": "./vectorstores/closevector/node.d.ts"}, "import": "./vectorstores/closevector/node.js", "require": "./vectorstores/closevector/node.cjs"}, "./vectorstores/chroma": {"types": {"import": "./vectorstores/chroma.d.ts", "require": "./vectorstores/chroma.d.cts", "default": "./vectorstores/chroma.d.ts"}, "import": "./vectorstores/chroma.js", "require": "./vectorstores/chroma.cjs"}, "./vectorstores/googlevertexai": {"types": {"import": "./vectorstores/googlevertexai.d.ts", "require": "./vectorstores/googlevertexai.d.cts", "default": "./vectorstores/googlevertexai.d.ts"}, "import": "./vectorstores/googlevertexai.js", "require": "./vectorstores/googlevertexai.cjs"}, "./vectorstores/hnswlib": {"types": {"import": "./vectorstores/hnswlib.d.ts", "require": "./vectorstores/hnswlib.d.cts", "default": "./vectorstores/hnswlib.d.ts"}, "import": "./vectorstores/hnswlib.js", "require": "./vectorstores/hnswlib.cjs"}, "./vectorstores/faiss": {"types": {"import": "./vectorstores/faiss.d.ts", "require": "./vectorstores/faiss.d.cts", "default": "./vectorstores/faiss.d.ts"}, "import": "./vectorstores/faiss.js", "require": "./vectorstores/faiss.cjs"}, "./vectorstores/weaviate": {"types": {"import": "./vectorstores/weaviate.d.ts", "require": "./vectorstores/weaviate.d.cts", "default": "./vectorstores/weaviate.d.ts"}, "import": "./vectorstores/weaviate.js", "require": "./vectorstores/weaviate.cjs"}, "./vectorstores/lancedb": {"types": {"import": "./vectorstores/lancedb.d.ts", "require": "./vectorstores/lancedb.d.cts", "default": "./vectorstores/lancedb.d.ts"}, "import": "./vectorstores/lancedb.js", "require": "./vectorstores/lancedb.cjs"}, "./vectorstores/momento_vector_index": {"types": {"import": "./vectorstores/momento_vector_index.d.ts", "require": "./vectorstores/momento_vector_index.d.cts", "default": "./vectorstores/momento_vector_index.d.ts"}, "import": "./vectorstores/momento_vector_index.js", "require": "./vectorstores/momento_vector_index.cjs"}, "./vectorstores/mongo": {"types": {"import": "./vectorstores/mongo.d.ts", "require": "./vectorstores/mongo.d.cts", "default": "./vectorstores/mongo.d.ts"}, "import": "./vectorstores/mongo.js", "require": "./vectorstores/mongo.cjs"}, "./vectorstores/mongodb_atlas": {"types": {"import": "./vectorstores/mongodb_atlas.d.ts", "require": "./vectorstores/mongodb_atlas.d.cts", "default": "./vectorstores/mongodb_atlas.d.ts"}, "import": "./vectorstores/mongodb_atlas.js", "require": "./vectorstores/mongodb_atlas.cjs"}, "./vectorstores/pinecone": {"types": {"import": "./vectorstores/pinecone.d.ts", "require": "./vectorstores/pinecone.d.cts", "default": "./vectorstores/pinecone.d.ts"}, "import": "./vectorstores/pinecone.js", "require": "./vectorstores/pinecone.cjs"}, "./vectorstores/qdrant": {"types": {"import": "./vectorstores/qdrant.d.ts", "require": "./vectorstores/qdrant.d.cts", "default": "./vectorstores/qdrant.d.ts"}, "import": "./vectorstores/qdrant.js", "require": "./vectorstores/qdrant.cjs"}, "./vectorstores/supabase": {"types": {"import": "./vectorstores/supabase.d.ts", "require": "./vectorstores/supabase.d.cts", "default": "./vectorstores/supabase.d.ts"}, "import": "./vectorstores/supabase.js", "require": "./vectorstores/supabase.cjs"}, "./vectorstores/opensearch": {"types": {"import": "./vectorstores/opensearch.d.ts", "require": "./vectorstores/opensearch.d.cts", "default": "./vectorstores/opensearch.d.ts"}, "import": "./vectorstores/opensearch.js", "require": "./vectorstores/opensearch.cjs"}, "./vectorstores/pgvector": {"types": {"import": "./vectorstores/pgvector.d.ts", "require": "./vectorstores/pgvector.d.cts", "default": "./vectorstores/pgvector.d.ts"}, "import": "./vectorstores/pgvector.js", "require": "./vectorstores/pgvector.cjs"}, "./vectorstores/milvus": {"types": {"import": "./vectorstores/milvus.d.ts", "require": "./vectorstores/milvus.d.cts", "default": "./vectorstores/milvus.d.ts"}, "import": "./vectorstores/milvus.js", "require": "./vectorstores/milvus.cjs"}, "./vectorstores/neo4j_vector": {"types": {"import": "./vectorstores/neo4j_vector.d.ts", "require": "./vectorstores/neo4j_vector.d.cts", "default": "./vectorstores/neo4j_vector.d.ts"}, "import": "./vectorstores/neo4j_vector.js", "require": "./vectorstores/neo4j_vector.cjs"}, "./vectorstores/prisma": {"types": {"import": "./vectorstores/prisma.d.ts", "require": "./vectorstores/prisma.d.cts", "default": "./vectorstores/prisma.d.ts"}, "import": "./vectorstores/prisma.js", "require": "./vectorstores/prisma.cjs"}, "./vectorstores/typeorm": {"types": {"import": "./vectorstores/typeorm.d.ts", "require": "./vectorstores/typeorm.d.cts", "default": "./vectorstores/typeorm.d.ts"}, "import": "./vectorstores/typeorm.js", "require": "./vectorstores/typeorm.cjs"}, "./vectorstores/myscale": {"types": {"import": "./vectorstores/myscale.d.ts", "require": "./vectorstores/myscale.d.cts", "default": "./vectorstores/myscale.d.ts"}, "import": "./vectorstores/myscale.js", "require": "./vectorstores/myscale.cjs"}, "./vectorstores/redis": {"types": {"import": "./vectorstores/redis.d.ts", "require": "./vectorstores/redis.d.cts", "default": "./vectorstores/redis.d.ts"}, "import": "./vectorstores/redis.js", "require": "./vectorstores/redis.cjs"}, "./vectorstores/rockset": {"types": {"import": "./vectorstores/rockset.d.ts", "require": "./vectorstores/rockset.d.cts", "default": "./vectorstores/rockset.d.ts"}, "import": "./vectorstores/rockset.js", "require": "./vectorstores/rockset.cjs"}, "./vectorstores/typesense": {"types": {"import": "./vectorstores/typesense.d.ts", "require": "./vectorstores/typesense.d.cts", "default": "./vectorstores/typesense.d.ts"}, "import": "./vectorstores/typesense.js", "require": "./vectorstores/typesense.cjs"}, "./vectorstores/singlestore": {"types": {"import": "./vectorstores/singlestore.d.ts", "require": "./vectorstores/singlestore.d.cts", "default": "./vectorstores/singlestore.d.ts"}, "import": "./vectorstores/singlestore.js", "require": "./vectorstores/singlestore.cjs"}, "./vectorstores/tigris": {"types": {"import": "./vectorstores/tigris.d.ts", "require": "./vectorstores/tigris.d.cts", "default": "./vectorstores/tigris.d.ts"}, "import": "./vectorstores/tigris.js", "require": "./vectorstores/tigris.cjs"}, "./vectorstores/usearch": {"types": {"import": "./vectorstores/usearch.d.ts", "require": "./vectorstores/usearch.d.cts", "default": "./vectorstores/usearch.d.ts"}, "import": "./vectorstores/usearch.js", "require": "./vectorstores/usearch.cjs"}, "./vectorstores/vectara": {"types": {"import": "./vectorstores/vectara.d.ts", "require": "./vectorstores/vectara.d.cts", "default": "./vectorstores/vectara.d.ts"}, "import": "./vectorstores/vectara.js", "require": "./vectorstores/vectara.cjs"}, "./vectorstores/vercel_postgres": {"types": {"import": "./vectorstores/vercel_postgres.d.ts", "require": "./vectorstores/vercel_postgres.d.cts", "default": "./vectorstores/vercel_postgres.d.ts"}, "import": "./vectorstores/vercel_postgres.js", "require": "./vectorstores/vercel_postgres.cjs"}, "./vectorstores/voy": {"types": {"import": "./vectorstores/voy.d.ts", "require": "./vectorstores/voy.d.cts", "default": "./vectorstores/voy.d.ts"}, "import": "./vectorstores/voy.js", "require": "./vectorstores/voy.cjs"}, "./vectorstores/xata": {"types": {"import": "./vectorstores/xata.d.ts", "require": "./vectorstores/xata.d.cts", "default": "./vectorstores/xata.d.ts"}, "import": "./vectorstores/xata.js", "require": "./vectorstores/xata.cjs"}, "./vectorstores/zep": {"types": {"import": "./vectorstores/zep.d.ts", "require": "./vectorstores/zep.d.cts", "default": "./vectorstores/zep.d.ts"}, "import": "./vectorstores/zep.js", "require": "./vectorstores/zep.cjs"}, "./text_splitter": {"types": {"import": "./text_splitter.d.ts", "require": "./text_splitter.d.cts", "default": "./text_splitter.d.ts"}, "import": "./text_splitter.js", "require": "./text_splitter.cjs"}, "./memory": {"types": {"import": "./memory.d.ts", "require": "./memory.d.cts", "default": "./memory.d.ts"}, "import": "./memory.js", "require": "./memory.cjs"}, "./memory/zep": {"types": {"import": "./memory/zep.d.ts", "require": "./memory/zep.d.cts", "default": "./memory/zep.d.ts"}, "import": "./memory/zep.js", "require": "./memory/zep.cjs"}, "./document": {"types": {"import": "./document.d.ts", "require": "./document.d.cts", "default": "./document.d.ts"}, "import": "./document.js", "require": "./document.cjs"}, "./document_loaders/base": {"types": {"import": "./document_loaders/base.d.ts", "require": "./document_loaders/base.d.cts", "default": "./document_loaders/base.d.ts"}, "import": "./document_loaders/base.js", "require": "./document_loaders/base.cjs"}, "./document_loaders/web/apify_dataset": {"types": {"import": "./document_loaders/web/apify_dataset.d.ts", "require": "./document_loaders/web/apify_dataset.d.cts", "default": "./document_loaders/web/apify_dataset.d.ts"}, "import": "./document_loaders/web/apify_dataset.js", "require": "./document_loaders/web/apify_dataset.cjs"}, "./document_loaders/web/assemblyai": {"types": {"import": "./document_loaders/web/assemblyai.d.ts", "require": "./document_loaders/web/assemblyai.d.cts", "default": "./document_loaders/web/assemblyai.d.ts"}, "import": "./document_loaders/web/assemblyai.js", "require": "./document_loaders/web/assemblyai.cjs"}, "./document_loaders/web/azure_blob_storage_container": {"types": {"import": "./document_loaders/web/azure_blob_storage_container.d.ts", "require": "./document_loaders/web/azure_blob_storage_container.d.cts", "default": "./document_loaders/web/azure_blob_storage_container.d.ts"}, "import": "./document_loaders/web/azure_blob_storage_container.js", "require": "./document_loaders/web/azure_blob_storage_container.cjs"}, "./document_loaders/web/azure_blob_storage_file": {"types": {"import": "./document_loaders/web/azure_blob_storage_file.d.ts", "require": "./document_loaders/web/azure_blob_storage_file.d.cts", "default": "./document_loaders/web/azure_blob_storage_file.d.ts"}, "import": "./document_loaders/web/azure_blob_storage_file.js", "require": "./document_loaders/web/azure_blob_storage_file.cjs"}, "./document_loaders/web/browserbase": {"types": {"import": "./document_loaders/web/browserbase.d.ts", "require": "./document_loaders/web/browserbase.d.cts", "default": "./document_loaders/web/browserbase.d.ts"}, "import": "./document_loaders/web/browserbase.js", "require": "./document_loaders/web/browserbase.cjs"}, "./document_loaders/web/cheerio": {"types": {"import": "./document_loaders/web/cheerio.d.ts", "require": "./document_loaders/web/cheerio.d.cts", "default": "./document_loaders/web/cheerio.d.ts"}, "import": "./document_loaders/web/cheerio.js", "require": "./document_loaders/web/cheerio.cjs"}, "./document_loaders/web/puppeteer": {"types": {"import": "./document_loaders/web/puppeteer.d.ts", "require": "./document_loaders/web/puppeteer.d.cts", "default": "./document_loaders/web/puppeteer.d.ts"}, "import": "./document_loaders/web/puppeteer.js", "require": "./document_loaders/web/puppeteer.cjs"}, "./document_loaders/web/playwright": {"types": {"import": "./document_loaders/web/playwright.d.ts", "require": "./document_loaders/web/playwright.d.cts", "default": "./document_loaders/web/playwright.d.ts"}, "import": "./document_loaders/web/playwright.js", "require": "./document_loaders/web/playwright.cjs"}, "./document_loaders/web/college_confidential": {"types": {"import": "./document_loaders/web/college_confidential.d.ts", "require": "./document_loaders/web/college_confidential.d.cts", "default": "./document_loaders/web/college_confidential.d.ts"}, "import": "./document_loaders/web/college_confidential.js", "require": "./document_loaders/web/college_confidential.cjs"}, "./document_loaders/web/gitbook": {"types": {"import": "./document_loaders/web/gitbook.d.ts", "require": "./document_loaders/web/gitbook.d.cts", "default": "./document_loaders/web/gitbook.d.ts"}, "import": "./document_loaders/web/gitbook.js", "require": "./document_loaders/web/gitbook.cjs"}, "./document_loaders/web/hn": {"types": {"import": "./document_loaders/web/hn.d.ts", "require": "./document_loaders/web/hn.d.cts", "default": "./document_loaders/web/hn.d.ts"}, "import": "./document_loaders/web/hn.js", "require": "./document_loaders/web/hn.cjs"}, "./document_loaders/web/imsdb": {"types": {"import": "./document_loaders/web/imsdb.d.ts", "require": "./document_loaders/web/imsdb.d.cts", "default": "./document_loaders/web/imsdb.d.ts"}, "import": "./document_loaders/web/imsdb.js", "require": "./document_loaders/web/imsdb.cjs"}, "./document_loaders/web/figma": {"types": {"import": "./document_loaders/web/figma.d.ts", "require": "./document_loaders/web/figma.d.cts", "default": "./document_loaders/web/figma.d.ts"}, "import": "./document_loaders/web/figma.js", "require": "./document_loaders/web/figma.cjs"}, "./document_loaders/web/firecrawl": {"types": {"import": "./document_loaders/web/firecrawl.d.ts", "require": "./document_loaders/web/firecrawl.d.cts", "default": "./document_loaders/web/firecrawl.d.ts"}, "import": "./document_loaders/web/firecrawl.js", "require": "./document_loaders/web/firecrawl.cjs"}, "./document_loaders/web/github": {"types": {"import": "./document_loaders/web/github.d.ts", "require": "./document_loaders/web/github.d.cts", "default": "./document_loaders/web/github.d.ts"}, "import": "./document_loaders/web/github.js", "require": "./document_loaders/web/github.cjs"}, "./document_loaders/web/notiondb": {"types": {"import": "./document_loaders/web/notiondb.d.ts", "require": "./document_loaders/web/notiondb.d.cts", "default": "./document_loaders/web/notiondb.d.ts"}, "import": "./document_loaders/web/notiondb.js", "require": "./document_loaders/web/notiondb.cjs"}, "./document_loaders/web/notionapi": {"types": {"import": "./document_loaders/web/notionapi.d.ts", "require": "./document_loaders/web/notionapi.d.cts", "default": "./document_loaders/web/notionapi.d.ts"}, "import": "./document_loaders/web/notionapi.js", "require": "./document_loaders/web/notionapi.cjs"}, "./document_loaders/web/pdf": {"types": {"import": "./document_loaders/web/pdf.d.ts", "require": "./document_loaders/web/pdf.d.cts", "default": "./document_loaders/web/pdf.d.ts"}, "import": "./document_loaders/web/pdf.js", "require": "./document_loaders/web/pdf.cjs"}, "./document_loaders/web/recursive_url": {"types": {"import": "./document_loaders/web/recursive_url.d.ts", "require": "./document_loaders/web/recursive_url.d.cts", "default": "./document_loaders/web/recursive_url.d.ts"}, "import": "./document_loaders/web/recursive_url.js", "require": "./document_loaders/web/recursive_url.cjs"}, "./document_loaders/web/s3": {"types": {"import": "./document_loaders/web/s3.d.ts", "require": "./document_loaders/web/s3.d.cts", "default": "./document_loaders/web/s3.d.ts"}, "import": "./document_loaders/web/s3.js", "require": "./document_loaders/web/s3.cjs"}, "./document_loaders/web/sitemap": {"types": {"import": "./document_loaders/web/sitemap.d.ts", "require": "./document_loaders/web/sitemap.d.cts", "default": "./document_loaders/web/sitemap.d.ts"}, "import": "./document_loaders/web/sitemap.js", "require": "./document_loaders/web/sitemap.cjs"}, "./document_loaders/web/sonix_audio": {"types": {"import": "./document_loaders/web/sonix_audio.d.ts", "require": "./document_loaders/web/sonix_audio.d.cts", "default": "./document_loaders/web/sonix_audio.d.ts"}, "import": "./document_loaders/web/sonix_audio.js", "require": "./document_loaders/web/sonix_audio.cjs"}, "./document_loaders/web/confluence": {"types": {"import": "./document_loaders/web/confluence.d.ts", "require": "./document_loaders/web/confluence.d.cts", "default": "./document_loaders/web/confluence.d.ts"}, "import": "./document_loaders/web/confluence.js", "require": "./document_loaders/web/confluence.cjs"}, "./document_loaders/web/couchbase": {"types": {"import": "./document_loaders/web/couchbase.d.ts", "require": "./document_loaders/web/couchbase.d.cts", "default": "./document_loaders/web/couchbase.d.ts"}, "import": "./document_loaders/web/couchbase.js", "require": "./document_loaders/web/couchbase.cjs"}, "./document_loaders/web/searchapi": {"types": {"import": "./document_loaders/web/searchapi.d.ts", "require": "./document_loaders/web/searchapi.d.cts", "default": "./document_loaders/web/searchapi.d.ts"}, "import": "./document_loaders/web/searchapi.js", "require": "./document_loaders/web/searchapi.cjs"}, "./document_loaders/web/serpapi": {"types": {"import": "./document_loaders/web/serpapi.d.ts", "require": "./document_loaders/web/serpapi.d.cts", "default": "./document_loaders/web/serpapi.d.ts"}, "import": "./document_loaders/web/serpapi.js", "require": "./document_loaders/web/serpapi.cjs"}, "./document_loaders/web/sort_xyz_blockchain": {"types": {"import": "./document_loaders/web/sort_xyz_blockchain.d.ts", "require": "./document_loaders/web/sort_xyz_blockchain.d.cts", "default": "./document_loaders/web/sort_xyz_blockchain.d.ts"}, "import": "./document_loaders/web/sort_xyz_blockchain.js", "require": "./document_loaders/web/sort_xyz_blockchain.cjs"}, "./document_loaders/web/youtube": {"types": {"import": "./document_loaders/web/youtube.d.ts", "require": "./document_loaders/web/youtube.d.cts", "default": "./document_loaders/web/youtube.d.ts"}, "import": "./document_loaders/web/youtube.js", "require": "./document_loaders/web/youtube.cjs"}, "./document_loaders/fs/directory": {"types": {"import": "./document_loaders/fs/directory.d.ts", "require": "./document_loaders/fs/directory.d.cts", "default": "./document_loaders/fs/directory.d.ts"}, "import": "./document_loaders/fs/directory.js", "require": "./document_loaders/fs/directory.cjs"}, "./document_loaders/fs/buffer": {"types": {"import": "./document_loaders/fs/buffer.d.ts", "require": "./document_loaders/fs/buffer.d.cts", "default": "./document_loaders/fs/buffer.d.ts"}, "import": "./document_loaders/fs/buffer.js", "require": "./document_loaders/fs/buffer.cjs"}, "./document_loaders/fs/chatgpt": {"types": {"import": "./document_loaders/fs/chatgpt.d.ts", "require": "./document_loaders/fs/chatgpt.d.cts", "default": "./document_loaders/fs/chatgpt.d.ts"}, "import": "./document_loaders/fs/chatgpt.js", "require": "./document_loaders/fs/chatgpt.cjs"}, "./document_loaders/fs/text": {"types": {"import": "./document_loaders/fs/text.d.ts", "require": "./document_loaders/fs/text.d.cts", "default": "./document_loaders/fs/text.d.ts"}, "import": "./document_loaders/fs/text.js", "require": "./document_loaders/fs/text.cjs"}, "./document_loaders/fs/json": {"types": {"import": "./document_loaders/fs/json.d.ts", "require": "./document_loaders/fs/json.d.cts", "default": "./document_loaders/fs/json.d.ts"}, "import": "./document_loaders/fs/json.js", "require": "./document_loaders/fs/json.cjs"}, "./document_loaders/fs/srt": {"types": {"import": "./document_loaders/fs/srt.d.ts", "require": "./document_loaders/fs/srt.d.cts", "default": "./document_loaders/fs/srt.d.ts"}, "import": "./document_loaders/fs/srt.js", "require": "./document_loaders/fs/srt.cjs"}, "./document_loaders/fs/pdf": {"types": {"import": "./document_loaders/fs/pdf.d.ts", "require": "./document_loaders/fs/pdf.d.cts", "default": "./document_loaders/fs/pdf.d.ts"}, "import": "./document_loaders/fs/pdf.js", "require": "./document_loaders/fs/pdf.cjs"}, "./document_loaders/fs/docx": {"types": {"import": "./document_loaders/fs/docx.d.ts", "require": "./document_loaders/fs/docx.d.cts", "default": "./document_loaders/fs/docx.d.ts"}, "import": "./document_loaders/fs/docx.js", "require": "./document_loaders/fs/docx.cjs"}, "./document_loaders/fs/epub": {"types": {"import": "./document_loaders/fs/epub.d.ts", "require": "./document_loaders/fs/epub.d.cts", "default": "./document_loaders/fs/epub.d.ts"}, "import": "./document_loaders/fs/epub.js", "require": "./document_loaders/fs/epub.cjs"}, "./document_loaders/fs/csv": {"types": {"import": "./document_loaders/fs/csv.d.ts", "require": "./document_loaders/fs/csv.d.cts", "default": "./document_loaders/fs/csv.d.ts"}, "import": "./document_loaders/fs/csv.js", "require": "./document_loaders/fs/csv.cjs"}, "./document_loaders/fs/notion": {"types": {"import": "./document_loaders/fs/notion.d.ts", "require": "./document_loaders/fs/notion.d.cts", "default": "./document_loaders/fs/notion.d.ts"}, "import": "./document_loaders/fs/notion.js", "require": "./document_loaders/fs/notion.cjs"}, "./document_loaders/fs/obsidian": {"types": {"import": "./document_loaders/fs/obsidian.d.ts", "require": "./document_loaders/fs/obsidian.d.cts", "default": "./document_loaders/fs/obsidian.d.ts"}, "import": "./document_loaders/fs/obsidian.js", "require": "./document_loaders/fs/obsidian.cjs"}, "./document_loaders/fs/unstructured": {"types": {"import": "./document_loaders/fs/unstructured.d.ts", "require": "./document_loaders/fs/unstructured.d.cts", "default": "./document_loaders/fs/unstructured.d.ts"}, "import": "./document_loaders/fs/unstructured.js", "require": "./document_loaders/fs/unstructured.cjs"}, "./document_loaders/fs/openai_whisper_audio": {"types": {"import": "./document_loaders/fs/openai_whisper_audio.d.ts", "require": "./document_loaders/fs/openai_whisper_audio.d.cts", "default": "./document_loaders/fs/openai_whisper_audio.d.ts"}, "import": "./document_loaders/fs/openai_whisper_audio.js", "require": "./document_loaders/fs/openai_whisper_audio.cjs"}, "./document_loaders/fs/pptx": {"types": {"import": "./document_loaders/fs/pptx.d.ts", "require": "./document_loaders/fs/pptx.d.cts", "default": "./document_loaders/fs/pptx.d.ts"}, "import": "./document_loaders/fs/pptx.js", "require": "./document_loaders/fs/pptx.cjs"}, "./document_transformers/html_to_text": {"types": {"import": "./document_transformers/html_to_text.d.ts", "require": "./document_transformers/html_to_text.d.cts", "default": "./document_transformers/html_to_text.d.ts"}, "import": "./document_transformers/html_to_text.js", "require": "./document_transformers/html_to_text.cjs"}, "./document_transformers/mozilla_readability": {"types": {"import": "./document_transformers/mozilla_readability.d.ts", "require": "./document_transformers/mozilla_readability.d.cts", "default": "./document_transformers/mozilla_readability.d.ts"}, "import": "./document_transformers/mozilla_readability.js", "require": "./document_transformers/mozilla_readability.cjs"}, "./document_transformers/openai_functions": {"types": {"import": "./document_transformers/openai_functions.d.ts", "require": "./document_transformers/openai_functions.d.cts", "default": "./document_transformers/openai_functions.d.ts"}, "import": "./document_transformers/openai_functions.js", "require": "./document_transformers/openai_functions.cjs"}, "./chat_models/base": {"types": {"import": "./chat_models/base.d.ts", "require": "./chat_models/base.d.cts", "default": "./chat_models/base.d.ts"}, "import": "./chat_models/base.js", "require": "./chat_models/base.cjs"}, "./chat_models/openai": {"types": {"import": "./chat_models/openai.d.ts", "require": "./chat_models/openai.d.cts", "default": "./chat_models/openai.d.ts"}, "import": "./chat_models/openai.js", "require": "./chat_models/openai.cjs"}, "./chat_models/portkey": {"types": {"import": "./chat_models/portkey.d.ts", "require": "./chat_models/portkey.d.cts", "default": "./chat_models/portkey.d.ts"}, "import": "./chat_models/portkey.js", "require": "./chat_models/portkey.cjs"}, "./chat_models/anthropic": {"types": {"import": "./chat_models/anthropic.d.ts", "require": "./chat_models/anthropic.d.cts", "default": "./chat_models/anthropic.d.ts"}, "import": "./chat_models/anthropic.js", "require": "./chat_models/anthropic.cjs"}, "./chat_models/bedrock": {"types": {"import": "./chat_models/bedrock.d.ts", "require": "./chat_models/bedrock.d.cts", "default": "./chat_models/bedrock.d.ts"}, "import": "./chat_models/bedrock.js", "require": "./chat_models/bedrock.cjs"}, "./chat_models/bedrock/web": {"types": {"import": "./chat_models/bedrock/web.d.ts", "require": "./chat_models/bedrock/web.d.cts", "default": "./chat_models/bedrock/web.d.ts"}, "import": "./chat_models/bedrock/web.js", "require": "./chat_models/bedrock/web.cjs"}, "./chat_models/cloudflare_workersai": {"types": {"import": "./chat_models/cloudflare_workersai.d.ts", "require": "./chat_models/cloudflare_workersai.d.cts", "default": "./chat_models/cloudflare_workersai.d.ts"}, "import": "./chat_models/cloudflare_workersai.js", "require": "./chat_models/cloudflare_workersai.cjs"}, "./chat_models/googlevertexai": {"types": {"import": "./chat_models/googlevertexai.d.ts", "require": "./chat_models/googlevertexai.d.cts", "default": "./chat_models/googlevertexai.d.ts"}, "import": "./chat_models/googlevertexai.js", "require": "./chat_models/googlevertexai.cjs"}, "./chat_models/googlevertexai/web": {"types": {"import": "./chat_models/googlevertexai/web.d.ts", "require": "./chat_models/googlevertexai/web.d.cts", "default": "./chat_models/googlevertexai/web.d.ts"}, "import": "./chat_models/googlevertexai/web.js", "require": "./chat_models/googlevertexai/web.cjs"}, "./chat_models/googlepalm": {"types": {"import": "./chat_models/googlepalm.d.ts", "require": "./chat_models/googlepalm.d.cts", "default": "./chat_models/googlepalm.d.ts"}, "import": "./chat_models/googlepalm.js", "require": "./chat_models/googlepalm.cjs"}, "./chat_models/fireworks": {"types": {"import": "./chat_models/fireworks.d.ts", "require": "./chat_models/fireworks.d.cts", "default": "./chat_models/fireworks.d.ts"}, "import": "./chat_models/fireworks.js", "require": "./chat_models/fireworks.cjs"}, "./chat_models/baiduwenxin": {"types": {"import": "./chat_models/baiduwenxin.d.ts", "require": "./chat_models/baiduwenxin.d.cts", "default": "./chat_models/baiduwenxin.d.ts"}, "import": "./chat_models/baiduwenxin.js", "require": "./chat_models/baiduwenxin.cjs"}, "./chat_models/iflytek_xinghuo": {"types": {"import": "./chat_models/iflytek_xinghuo.d.ts", "require": "./chat_models/iflytek_xinghuo.d.cts", "default": "./chat_models/iflytek_xinghuo.d.ts"}, "import": "./chat_models/iflytek_xinghuo.js", "require": "./chat_models/iflytek_xinghuo.cjs"}, "./chat_models/iflytek_xinghuo/web": {"types": {"import": "./chat_models/iflytek_xinghuo/web.d.ts", "require": "./chat_models/iflytek_xinghuo/web.d.cts", "default": "./chat_models/iflytek_xinghuo/web.d.ts"}, "import": "./chat_models/iflytek_xinghuo/web.js", "require": "./chat_models/iflytek_xinghuo/web.cjs"}, "./chat_models/ollama": {"types": {"import": "./chat_models/ollama.d.ts", "require": "./chat_models/ollama.d.cts", "default": "./chat_models/ollama.d.ts"}, "import": "./chat_models/ollama.js", "require": "./chat_models/ollama.cjs"}, "./chat_models/minimax": {"types": {"import": "./chat_models/minimax.d.ts", "require": "./chat_models/minimax.d.cts", "default": "./chat_models/minimax.d.ts"}, "import": "./chat_models/minimax.js", "require": "./chat_models/minimax.cjs"}, "./chat_models/llama_cpp": {"types": {"import": "./chat_models/llama_cpp.d.ts", "require": "./chat_models/llama_cpp.d.cts", "default": "./chat_models/llama_cpp.d.ts"}, "import": "./chat_models/llama_cpp.js", "require": "./chat_models/llama_cpp.cjs"}, "./chat_models/yandex": {"types": {"import": "./chat_models/yandex.d.ts", "require": "./chat_models/yandex.d.cts", "default": "./chat_models/yandex.d.ts"}, "import": "./chat_models/yandex.js", "require": "./chat_models/yandex.cjs"}, "./chat_models/fake": {"types": {"import": "./chat_models/fake.d.ts", "require": "./chat_models/fake.d.cts", "default": "./chat_models/fake.d.ts"}, "import": "./chat_models/fake.js", "require": "./chat_models/fake.cjs"}, "./schema": {"types": {"import": "./schema.d.ts", "require": "./schema.d.cts", "default": "./schema.d.ts"}, "import": "./schema.js", "require": "./schema.cjs"}, "./schema/document": {"types": {"import": "./schema/document.d.ts", "require": "./schema/document.d.cts", "default": "./schema/document.d.ts"}, "import": "./schema/document.js", "require": "./schema/document.cjs"}, "./schema/output_parser": {"types": {"import": "./schema/output_parser.d.ts", "require": "./schema/output_parser.d.cts", "default": "./schema/output_parser.d.ts"}, "import": "./schema/output_parser.js", "require": "./schema/output_parser.cjs"}, "./schema/prompt_template": {"types": {"import": "./schema/prompt_template.d.ts", "require": "./schema/prompt_template.d.cts", "default": "./schema/prompt_template.d.ts"}, "import": "./schema/prompt_template.js", "require": "./schema/prompt_template.cjs"}, "./schema/query_constructor": {"types": {"import": "./schema/query_constructor.d.ts", "require": "./schema/query_constructor.d.cts", "default": "./schema/query_constructor.d.ts"}, "import": "./schema/query_constructor.js", "require": "./schema/query_constructor.cjs"}, "./schema/retriever": {"types": {"import": "./schema/retriever.d.ts", "require": "./schema/retriever.d.cts", "default": "./schema/retriever.d.ts"}, "import": "./schema/retriever.js", "require": "./schema/retriever.cjs"}, "./schema/runnable": {"types": {"import": "./schema/runnable.d.ts", "require": "./schema/runnable.d.cts", "default": "./schema/runnable.d.ts"}, "import": "./schema/runnable.js", "require": "./schema/runnable.cjs"}, "./schema/storage": {"types": {"import": "./schema/storage.d.ts", "require": "./schema/storage.d.cts", "default": "./schema/storage.d.ts"}, "import": "./schema/storage.js", "require": "./schema/storage.cjs"}, "./sql_db": {"types": {"import": "./sql_db.d.ts", "require": "./sql_db.d.cts", "default": "./sql_db.d.ts"}, "import": "./sql_db.js", "require": "./sql_db.cjs"}, "./callbacks": {"types": {"import": "./callbacks.d.ts", "require": "./callbacks.d.cts", "default": "./callbacks.d.ts"}, "import": "./callbacks.js", "require": "./callbacks.cjs"}, "./callbacks/handlers/llmonitor": {"types": {"import": "./callbacks/handlers/llmonitor.d.ts", "require": "./callbacks/handlers/llmonitor.d.cts", "default": "./callbacks/handlers/llmonitor.d.ts"}, "import": "./callbacks/handlers/llmonitor.js", "require": "./callbacks/handlers/llmonitor.cjs"}, "./output_parsers": {"types": {"import": "./output_parsers.d.ts", "require": "./output_parsers.d.cts", "default": "./output_parsers.d.ts"}, "import": "./output_parsers.js", "require": "./output_parsers.cjs"}, "./output_parsers/expression": {"types": {"import": "./output_parsers/expression.d.ts", "require": "./output_parsers/expression.d.cts", "default": "./output_parsers/expression.d.ts"}, "import": "./output_parsers/expression.js", "require": "./output_parsers/expression.cjs"}, "./retrievers/amazon_kendra": {"types": {"import": "./retrievers/amazon_kendra.d.ts", "require": "./retrievers/amazon_kendra.d.cts", "default": "./retrievers/amazon_kendra.d.ts"}, "import": "./retrievers/amazon_kendra.js", "require": "./retrievers/amazon_kendra.cjs"}, "./retrievers/remote": {"types": {"import": "./retrievers/remote.d.ts", "require": "./retrievers/remote.d.cts", "default": "./retrievers/remote.d.ts"}, "import": "./retrievers/remote.js", "require": "./retrievers/remote.cjs"}, "./retrievers/supabase": {"types": {"import": "./retrievers/supabase.d.ts", "require": "./retrievers/supabase.d.cts", "default": "./retrievers/supabase.d.ts"}, "import": "./retrievers/supabase.js", "require": "./retrievers/supabase.cjs"}, "./retrievers/zep": {"types": {"import": "./retrievers/zep.d.ts", "require": "./retrievers/zep.d.cts", "default": "./retrievers/zep.d.ts"}, "import": "./retrievers/zep.js", "require": "./retrievers/zep.cjs"}, "./retrievers/metal": {"types": {"import": "./retrievers/metal.d.ts", "require": "./retrievers/metal.d.cts", "default": "./retrievers/metal.d.ts"}, "import": "./retrievers/metal.js", "require": "./retrievers/metal.cjs"}, "./retrievers/chaindesk": {"types": {"import": "./retrievers/chaindesk.d.ts", "require": "./retrievers/chaindesk.d.cts", "default": "./retrievers/chaindesk.d.ts"}, "import": "./retrievers/chaindesk.js", "require": "./retrievers/chaindesk.cjs"}, "./retrievers/databerry": {"types": {"import": "./retrievers/databerry.d.ts", "require": "./retrievers/databerry.d.cts", "default": "./retrievers/databerry.d.ts"}, "import": "./retrievers/databerry.js", "require": "./retrievers/databerry.cjs"}, "./retrievers/contextual_compression": {"types": {"import": "./retrievers/contextual_compression.d.ts", "require": "./retrievers/contextual_compression.d.cts", "default": "./retrievers/contextual_compression.d.ts"}, "import": "./retrievers/contextual_compression.js", "require": "./retrievers/contextual_compression.cjs"}, "./retrievers/document_compressors": {"types": {"import": "./retrievers/document_compressors.d.ts", "require": "./retrievers/document_compressors.d.cts", "default": "./retrievers/document_compressors.d.ts"}, "import": "./retrievers/document_compressors.js", "require": "./retrievers/document_compressors.cjs"}, "./retrievers/multi_query": {"types": {"import": "./retrievers/multi_query.d.ts", "require": "./retrievers/multi_query.d.cts", "default": "./retrievers/multi_query.d.ts"}, "import": "./retrievers/multi_query.js", "require": "./retrievers/multi_query.cjs"}, "./retrievers/multi_vector": {"types": {"import": "./retrievers/multi_vector.d.ts", "require": "./retrievers/multi_vector.d.cts", "default": "./retrievers/multi_vector.d.ts"}, "import": "./retrievers/multi_vector.js", "require": "./retrievers/multi_vector.cjs"}, "./retrievers/parent_document": {"types": {"import": "./retrievers/parent_document.d.ts", "require": "./retrievers/parent_document.d.cts", "default": "./retrievers/parent_document.d.ts"}, "import": "./retrievers/parent_document.js", "require": "./retrievers/parent_document.cjs"}, "./retrievers/vectara_summary": {"types": {"import": "./retrievers/vectara_summary.d.ts", "require": "./retrievers/vectara_summary.d.cts", "default": "./retrievers/vectara_summary.d.ts"}, "import": "./retrievers/vectara_summary.js", "require": "./retrievers/vectara_summary.cjs"}, "./retrievers/tavily_search_api": {"types": {"import": "./retrievers/tavily_search_api.d.ts", "require": "./retrievers/tavily_search_api.d.cts", "default": "./retrievers/tavily_search_api.d.ts"}, "import": "./retrievers/tavily_search_api.js", "require": "./retrievers/tavily_search_api.cjs"}, "./retrievers/time_weighted": {"types": {"import": "./retrievers/time_weighted.d.ts", "require": "./retrievers/time_weighted.d.cts", "default": "./retrievers/time_weighted.d.ts"}, "import": "./retrievers/time_weighted.js", "require": "./retrievers/time_weighted.cjs"}, "./retrievers/document_compressors/chain_extract": {"types": {"import": "./retrievers/document_compressors/chain_extract.d.ts", "require": "./retrievers/document_compressors/chain_extract.d.cts", "default": "./retrievers/document_compressors/chain_extract.d.ts"}, "import": "./retrievers/document_compressors/chain_extract.js", "require": "./retrievers/document_compressors/chain_extract.cjs"}, "./retrievers/document_compressors/embeddings_filter": {"types": {"import": "./retrievers/document_compressors/embeddings_filter.d.ts", "require": "./retrievers/document_compressors/embeddings_filter.d.cts", "default": "./retrievers/document_compressors/embeddings_filter.d.ts"}, "import": "./retrievers/document_compressors/embeddings_filter.js", "require": "./retrievers/document_compressors/embeddings_filter.cjs"}, "./retrievers/hyde": {"types": {"import": "./retrievers/hyde.d.ts", "require": "./retrievers/hyde.d.cts", "default": "./retrievers/hyde.d.ts"}, "import": "./retrievers/hyde.js", "require": "./retrievers/hyde.cjs"}, "./retrievers/score_threshold": {"types": {"import": "./retrievers/score_threshold.d.ts", "require": "./retrievers/score_threshold.d.cts", "default": "./retrievers/score_threshold.d.ts"}, "import": "./retrievers/score_threshold.js", "require": "./retrievers/score_threshold.cjs"}, "./retrievers/self_query": {"types": {"import": "./retrievers/self_query.d.ts", "require": "./retrievers/self_query.d.cts", "default": "./retrievers/self_query.d.ts"}, "import": "./retrievers/self_query.js", "require": "./retrievers/self_query.cjs"}, "./retrievers/self_query/chroma": {"types": {"import": "./retrievers/self_query/chroma.d.ts", "require": "./retrievers/self_query/chroma.d.cts", "default": "./retrievers/self_query/chroma.d.ts"}, "import": "./retrievers/self_query/chroma.js", "require": "./retrievers/self_query/chroma.cjs"}, "./retrievers/self_query/functional": {"types": {"import": "./retrievers/self_query/functional.d.ts", "require": "./retrievers/self_query/functional.d.cts", "default": "./retrievers/self_query/functional.d.ts"}, "import": "./retrievers/self_query/functional.js", "require": "./retrievers/self_query/functional.cjs"}, "./retrievers/self_query/pinecone": {"types": {"import": "./retrievers/self_query/pinecone.d.ts", "require": "./retrievers/self_query/pinecone.d.cts", "default": "./retrievers/self_query/pinecone.d.ts"}, "import": "./retrievers/self_query/pinecone.js", "require": "./retrievers/self_query/pinecone.cjs"}, "./retrievers/self_query/supabase": {"types": {"import": "./retrievers/self_query/supabase.d.ts", "require": "./retrievers/self_query/supabase.d.cts", "default": "./retrievers/self_query/supabase.d.ts"}, "import": "./retrievers/self_query/supabase.js", "require": "./retrievers/self_query/supabase.cjs"}, "./retrievers/self_query/weaviate": {"types": {"import": "./retrievers/self_query/weaviate.d.ts", "require": "./retrievers/self_query/weaviate.d.cts", "default": "./retrievers/self_query/weaviate.d.ts"}, "import": "./retrievers/self_query/weaviate.js", "require": "./retrievers/self_query/weaviate.cjs"}, "./retrievers/self_query/vectara": {"types": {"import": "./retrievers/self_query/vectara.d.ts", "require": "./retrievers/self_query/vectara.d.cts", "default": "./retrievers/self_query/vectara.d.ts"}, "import": "./retrievers/self_query/vectara.js", "require": "./retrievers/self_query/vectara.cjs"}, "./retrievers/vespa": {"types": {"import": "./retrievers/vespa.d.ts", "require": "./retrievers/vespa.d.cts", "default": "./retrievers/vespa.d.ts"}, "import": "./retrievers/vespa.js", "require": "./retrievers/vespa.cjs"}, "./retrievers/matryoshka_retriever": {"types": {"import": "./retrievers/matryoshka_retriever.d.ts", "require": "./retrievers/matryoshka_retriever.d.cts", "default": "./retrievers/matryoshka_retriever.d.ts"}, "import": "./retrievers/matryoshka_retriever.js", "require": "./retrievers/matryoshka_retriever.cjs"}, "./cache": {"types": {"import": "./cache.d.ts", "require": "./cache.d.cts", "default": "./cache.d.ts"}, "import": "./cache.js", "require": "./cache.cjs"}, "./cache/cloudflare_kv": {"types": {"import": "./cache/cloudflare_kv.d.ts", "require": "./cache/cloudflare_kv.d.cts", "default": "./cache/cloudflare_kv.d.ts"}, "import": "./cache/cloudflare_kv.js", "require": "./cache/cloudflare_kv.cjs"}, "./cache/momento": {"types": {"import": "./cache/momento.d.ts", "require": "./cache/momento.d.cts", "default": "./cache/momento.d.ts"}, "import": "./cache/momento.js", "require": "./cache/momento.cjs"}, "./cache/redis": {"types": {"import": "./cache/redis.d.ts", "require": "./cache/redis.d.cts", "default": "./cache/redis.d.ts"}, "import": "./cache/redis.js", "require": "./cache/redis.cjs"}, "./cache/ioredis": {"types": {"import": "./cache/ioredis.d.ts", "require": "./cache/ioredis.d.cts", "default": "./cache/ioredis.d.ts"}, "import": "./cache/ioredis.js", "require": "./cache/ioredis.cjs"}, "./cache/file_system": {"types": {"import": "./cache/file_system.d.ts", "require": "./cache/file_system.d.cts", "default": "./cache/file_system.d.ts"}, "import": "./cache/file_system.js", "require": "./cache/file_system.cjs"}, "./cache/upstash_redis": {"types": {"import": "./cache/upstash_redis.d.ts", "require": "./cache/upstash_redis.d.cts", "default": "./cache/upstash_redis.d.ts"}, "import": "./cache/upstash_redis.js", "require": "./cache/upstash_redis.cjs"}, "./stores/doc/in_memory": {"types": {"import": "./stores/doc/in_memory.d.ts", "require": "./stores/doc/in_memory.d.cts", "default": "./stores/doc/in_memory.d.ts"}, "import": "./stores/doc/in_memory.js", "require": "./stores/doc/in_memory.cjs"}, "./stores/doc/gcs": {"types": {"import": "./stores/doc/gcs.d.ts", "require": "./stores/doc/gcs.d.cts", "default": "./stores/doc/gcs.d.ts"}, "import": "./stores/doc/gcs.js", "require": "./stores/doc/gcs.cjs"}, "./stores/file/in_memory": {"types": {"import": "./stores/file/in_memory.d.ts", "require": "./stores/file/in_memory.d.cts", "default": "./stores/file/in_memory.d.ts"}, "import": "./stores/file/in_memory.js", "require": "./stores/file/in_memory.cjs"}, "./stores/file/node": {"types": {"import": "./stores/file/node.d.ts", "require": "./stores/file/node.d.cts", "default": "./stores/file/node.d.ts"}, "import": "./stores/file/node.js", "require": "./stores/file/node.cjs"}, "./stores/message/cassandra": {"types": {"import": "./stores/message/cassandra.d.ts", "require": "./stores/message/cassandra.d.cts", "default": "./stores/message/cassandra.d.ts"}, "import": "./stores/message/cassandra.js", "require": "./stores/message/cassandra.cjs"}, "./stores/message/convex": {"types": {"import": "./stores/message/convex.d.ts", "require": "./stores/message/convex.d.cts", "default": "./stores/message/convex.d.ts"}, "import": "./stores/message/convex.js", "require": "./stores/message/convex.cjs"}, "./stores/message/cloudflare_d1": {"types": {"import": "./stores/message/cloudflare_d1.d.ts", "require": "./stores/message/cloudflare_d1.d.cts", "default": "./stores/message/cloudflare_d1.d.ts"}, "import": "./stores/message/cloudflare_d1.js", "require": "./stores/message/cloudflare_d1.cjs"}, "./stores/message/in_memory": {"types": {"import": "./stores/message/in_memory.d.ts", "require": "./stores/message/in_memory.d.cts", "default": "./stores/message/in_memory.d.ts"}, "import": "./stores/message/in_memory.js", "require": "./stores/message/in_memory.cjs"}, "./stores/message/dynamodb": {"types": {"import": "./stores/message/dynamodb.d.ts", "require": "./stores/message/dynamodb.d.cts", "default": "./stores/message/dynamodb.d.ts"}, "import": "./stores/message/dynamodb.js", "require": "./stores/message/dynamodb.cjs"}, "./stores/message/firestore": {"types": {"import": "./stores/message/firestore.d.ts", "require": "./stores/message/firestore.d.cts", "default": "./stores/message/firestore.d.ts"}, "import": "./stores/message/firestore.js", "require": "./stores/message/firestore.cjs"}, "./stores/message/momento": {"types": {"import": "./stores/message/momento.d.ts", "require": "./stores/message/momento.d.cts", "default": "./stores/message/momento.d.ts"}, "import": "./stores/message/momento.js", "require": "./stores/message/momento.cjs"}, "./stores/message/mongodb": {"types": {"import": "./stores/message/mongodb.d.ts", "require": "./stores/message/mongodb.d.cts", "default": "./stores/message/mongodb.d.ts"}, "import": "./stores/message/mongodb.js", "require": "./stores/message/mongodb.cjs"}, "./stores/message/redis": {"types": {"import": "./stores/message/redis.d.ts", "require": "./stores/message/redis.d.cts", "default": "./stores/message/redis.d.ts"}, "import": "./stores/message/redis.js", "require": "./stores/message/redis.cjs"}, "./stores/message/ioredis": {"types": {"import": "./stores/message/ioredis.d.ts", "require": "./stores/message/ioredis.d.cts", "default": "./stores/message/ioredis.d.ts"}, "import": "./stores/message/ioredis.js", "require": "./stores/message/ioredis.cjs"}, "./stores/message/upstash_redis": {"types": {"import": "./stores/message/upstash_redis.d.ts", "require": "./stores/message/upstash_redis.d.cts", "default": "./stores/message/upstash_redis.d.ts"}, "import": "./stores/message/upstash_redis.js", "require": "./stores/message/upstash_redis.cjs"}, "./stores/message/planetscale": {"types": {"import": "./stores/message/planetscale.d.ts", "require": "./stores/message/planetscale.d.cts", "default": "./stores/message/planetscale.d.ts"}, "import": "./stores/message/planetscale.js", "require": "./stores/message/planetscale.cjs"}, "./stores/message/xata": {"types": {"import": "./stores/message/xata.d.ts", "require": "./stores/message/xata.d.cts", "default": "./stores/message/xata.d.ts"}, "import": "./stores/message/xata.js", "require": "./stores/message/xata.cjs"}, "./storage/convex": {"types": {"import": "./storage/convex.d.ts", "require": "./storage/convex.d.cts", "default": "./storage/convex.d.ts"}, "import": "./storage/convex.js", "require": "./storage/convex.cjs"}, "./storage/encoder_backed": {"types": {"import": "./storage/encoder_backed.d.ts", "require": "./storage/encoder_backed.d.cts", "default": "./storage/encoder_backed.d.ts"}, "import": "./storage/encoder_backed.js", "require": "./storage/encoder_backed.cjs"}, "./storage/in_memory": {"types": {"import": "./storage/in_memory.d.ts", "require": "./storage/in_memory.d.cts", "default": "./storage/in_memory.d.ts"}, "import": "./storage/in_memory.js", "require": "./storage/in_memory.cjs"}, "./storage/ioredis": {"types": {"import": "./storage/ioredis.d.ts", "require": "./storage/ioredis.d.cts", "default": "./storage/ioredis.d.ts"}, "import": "./storage/ioredis.js", "require": "./storage/ioredis.cjs"}, "./storage/vercel_kv": {"types": {"import": "./storage/vercel_kv.d.ts", "require": "./storage/vercel_kv.d.cts", "default": "./storage/vercel_kv.d.ts"}, "import": "./storage/vercel_kv.js", "require": "./storage/vercel_kv.cjs"}, "./storage/upstash_redis": {"types": {"import": "./storage/upstash_redis.d.ts", "require": "./storage/upstash_redis.d.cts", "default": "./storage/upstash_redis.d.ts"}, "import": "./storage/upstash_redis.js", "require": "./storage/upstash_redis.cjs"}, "./storage/file_system": {"types": {"import": "./storage/file_system.d.ts", "require": "./storage/file_system.d.cts", "default": "./storage/file_system.d.ts"}, "import": "./storage/file_system.js", "require": "./storage/file_system.cjs"}, "./graphs/neo4j_graph": {"types": {"import": "./graphs/neo4j_graph.d.ts", "require": "./graphs/neo4j_graph.d.cts", "default": "./graphs/neo4j_graph.d.ts"}, "import": "./graphs/neo4j_graph.js", "require": "./graphs/neo4j_graph.cjs"}, "./hub": {"types": {"import": "./hub.d.ts", "require": "./hub.d.cts", "default": "./hub.d.ts"}, "import": "./hub.js", "require": "./hub.cjs"}, "./util/convex": {"types": {"import": "./util/convex.d.ts", "require": "./util/convex.d.cts", "default": "./util/convex.d.ts"}, "import": "./util/convex.js", "require": "./util/convex.cjs"}, "./util/document": {"types": {"import": "./util/document.d.ts", "require": "./util/document.d.cts", "default": "./util/document.d.ts"}, "import": "./util/document.js", "require": "./util/document.cjs"}, "./util/math": {"types": {"import": "./util/math.d.ts", "require": "./util/math.d.cts", "default": "./util/math.d.ts"}, "import": "./util/math.js", "require": "./util/math.cjs"}, "./util/time": {"types": {"import": "./util/time.d.ts", "require": "./util/time.d.cts", "default": "./util/time.d.ts"}, "import": "./util/time.js", "require": "./util/time.cjs"}, "./experimental/autogpt": {"types": {"import": "./experimental/autogpt.d.ts", "require": "./experimental/autogpt.d.cts", "default": "./experimental/autogpt.d.ts"}, "import": "./experimental/autogpt.js", "require": "./experimental/autogpt.cjs"}, "./experimental/openai_assistant": {"types": {"import": "./experimental/openai_assistant.d.ts", "require": "./experimental/openai_assistant.d.cts", "default": "./experimental/openai_assistant.d.ts"}, "import": "./experimental/openai_assistant.js", "require": "./experimental/openai_assistant.cjs"}, "./experimental/openai_files": {"types": {"import": "./experimental/openai_files.d.ts", "require": "./experimental/openai_files.d.cts", "default": "./experimental/openai_files.d.ts"}, "import": "./experimental/openai_files.js", "require": "./experimental/openai_files.cjs"}, "./experimental/babyagi": {"types": {"import": "./experimental/babyagi.d.ts", "require": "./experimental/babyagi.d.cts", "default": "./experimental/babyagi.d.ts"}, "import": "./experimental/babyagi.js", "require": "./experimental/babyagi.cjs"}, "./experimental/generative_agents": {"types": {"import": "./experimental/generative_agents.d.ts", "require": "./experimental/generative_agents.d.cts", "default": "./experimental/generative_agents.d.ts"}, "import": "./experimental/generative_agents.js", "require": "./experimental/generative_agents.cjs"}, "./experimental/plan_and_execute": {"types": {"import": "./experimental/plan_and_execute.d.ts", "require": "./experimental/plan_and_execute.d.cts", "default": "./experimental/plan_and_execute.d.ts"}, "import": "./experimental/plan_and_execute.js", "require": "./experimental/plan_and_execute.cjs"}, "./experimental/multimodal_embeddings/googlevertexai": {"types": {"import": "./experimental/multimodal_embeddings/googlevertexai.d.ts", "require": "./experimental/multimodal_embeddings/googlevertexai.d.cts", "default": "./experimental/multimodal_embeddings/googlevertexai.d.ts"}, "import": "./experimental/multimodal_embeddings/googlevertexai.js", "require": "./experimental/multimodal_embeddings/googlevertexai.cjs"}, "./experimental/chat_models/anthropic_functions": {"types": {"import": "./experimental/chat_models/anthropic_functions.d.ts", "require": "./experimental/chat_models/anthropic_functions.d.cts", "default": "./experimental/chat_models/anthropic_functions.d.ts"}, "import": "./experimental/chat_models/anthropic_functions.js", "require": "./experimental/chat_models/anthropic_functions.cjs"}, "./experimental/chat_models/bittensor": {"types": {"import": "./experimental/chat_models/bittensor.d.ts", "require": "./experimental/chat_models/bittensor.d.cts", "default": "./experimental/chat_models/bittensor.d.ts"}, "import": "./experimental/chat_models/bittensor.js", "require": "./experimental/chat_models/bittensor.cjs"}, "./experimental/chat_models/ollama_functions": {"types": {"import": "./experimental/chat_models/ollama_functions.d.ts", "require": "./experimental/chat_models/ollama_functions.d.cts", "default": "./experimental/chat_models/ollama_functions.d.ts"}, "import": "./experimental/chat_models/ollama_functions.js", "require": "./experimental/chat_models/ollama_functions.cjs"}, "./experimental/llms/bittensor": {"types": {"import": "./experimental/llms/bittensor.d.ts", "require": "./experimental/llms/bittensor.d.cts", "default": "./experimental/llms/bittensor.d.ts"}, "import": "./experimental/llms/bittensor.js", "require": "./experimental/llms/bittensor.cjs"}, "./experimental/hubs/makersuite/googlemakersuitehub": {"types": {"import": "./experimental/hubs/makersuite/googlemakersuitehub.d.ts", "require": "./experimental/hubs/makersuite/googlemakersuitehub.d.cts", "default": "./experimental/hubs/makersuite/googlemakersuitehub.d.ts"}, "import": "./experimental/hubs/makersuite/googlemakersuitehub.js", "require": "./experimental/hubs/makersuite/googlemakersuitehub.cjs"}, "./experimental/chains/violation_of_expectations": {"types": {"import": "./experimental/chains/violation_of_expectations.d.ts", "require": "./experimental/chains/violation_of_expectations.d.cts", "default": "./experimental/chains/violation_of_expectations.d.ts"}, "import": "./experimental/chains/violation_of_expectations.js", "require": "./experimental/chains/violation_of_expectations.cjs"}, "./experimental/masking": {"types": {"import": "./experimental/masking.d.ts", "require": "./experimental/masking.d.cts", "default": "./experimental/masking.d.ts"}, "import": "./experimental/masking.js", "require": "./experimental/masking.cjs"}, "./experimental/prompts/custom_format": {"types": {"import": "./experimental/prompts/custom_format.d.ts", "require": "./experimental/prompts/custom_format.d.cts", "default": "./experimental/prompts/custom_format.d.ts"}, "import": "./experimental/prompts/custom_format.js", "require": "./experimental/prompts/custom_format.cjs"}, "./experimental/prompts/handlebars": {"types": {"import": "./experimental/prompts/handlebars.d.ts", "require": "./experimental/prompts/handlebars.d.cts", "default": "./experimental/prompts/handlebars.d.ts"}, "import": "./experimental/prompts/handlebars.js", "require": "./experimental/prompts/handlebars.cjs"}, "./experimental/tools/pyinterpreter": {"types": {"import": "./experimental/tools/pyinterpreter.d.ts", "require": "./experimental/tools/pyinterpreter.d.cts", "default": "./experimental/tools/pyinterpreter.d.ts"}, "import": "./experimental/tools/pyinterpreter.js", "require": "./experimental/tools/pyinterpreter.cjs"}, "./evaluation": {"types": {"import": "./evaluation.d.ts", "require": "./evaluation.d.cts", "default": "./evaluation.d.ts"}, "import": "./evaluation.js", "require": "./evaluation.cjs"}, "./smith": {"types": {"import": "./smith.d.ts", "require": "./smith.d.cts", "default": "./smith.d.ts"}, "import": "./smith.js", "require": "./smith.cjs"}, "./runnables": {"types": {"import": "./runnables.d.ts", "require": "./runnables.d.cts", "default": "./runnables.d.ts"}, "import": "./runnables.js", "require": "./runnables.cjs"}, "./runnables/remote": {"types": {"import": "./runnables/remote.d.ts", "require": "./runnables/remote.d.cts", "default": "./runnables/remote.d.ts"}, "import": "./runnables/remote.js", "require": "./runnables/remote.cjs"}, "./indexes": {"types": {"import": "./indexes.d.ts", "require": "./indexes.d.cts", "default": "./indexes.d.ts"}, "import": "./indexes.js", "require": "./indexes.cjs"}, "./package.json": "./package.json"}}