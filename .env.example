# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
# 如果使用代理或第三方服务，请修改上面的base URL，例如：
# OPENAI_BASE_URL=https://your-proxy-domain.com/v1
# OPENAI_BASE_URL=https://api.openai-proxy.com/v1

# OpenAI模型配置
OPENAI_MODEL=gpt-4
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
OPENAI_TEMPERATURE=0.3
OPENAI_MAX_TOKENS=2000
OPENAI_TOP_P=1
OPENAI_FREQUENCY_PENALTY=0
OPENAI_PRESENCE_PENALTY=0

# PineCone配置
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here
PINECONE_INDEX_NAME=translation-knowledge

# 服务端口配置
PDF_SERVICE_PORT=3001
PINECONE_SERVICE_PORT=3002
TRANSLATOR_SERVICE_PORT=3003
PROCESSOR_SERVICE_PORT=3004

# N8N配置
N8N_HOST=localhost
N8N_PORT=5678

# 文件存储配置
UPLOAD_DIR=./uploads
OUTPUT_DIR=./outputs
MAX_FILE_SIZE=50MB

# 翻译配置
MAX_SEGMENT_LENGTH=2000
TRANSLATION_TIMEOUT=30000
BATCH_SIZE=10

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/translation.log

# 安全配置
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=http://localhost:3000
