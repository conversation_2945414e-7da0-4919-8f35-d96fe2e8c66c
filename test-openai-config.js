#!/usr/bin/env node

/**
 * OpenAI配置测试工具
 * 用于验证OpenAI API配置是否正确
 */

require('dotenv').config();
const openaiConfig = require('./config/openai-config');

async function testOpenAIConfiguration() {
  console.log('🔧 OpenAI配置测试工具');
  console.log('='.repeat(50));

  try {
    // 1. 显示当前配置信息
    console.log('\n📋 当前配置信息:');
    const configInfo = openaiConfig.getConfigInfo();
    console.log(`   API Key: ${configInfo.hasApiKey ? '✅ 已设置' : '❌ 未设置'}`);
    console.log(`   Base URL: ${configInfo.baseURL}`);
    console.log(`   模型: ${configInfo.model}`);
    console.log(`   嵌入模型: ${configInfo.embeddingModel}`);
    console.log(`   温度: ${configInfo.temperature}`);
    console.log(`   最大Token: ${configInfo.maxTokens}`);

    if (!configInfo.hasApiKey) {
      console.log('\n❌ 错误: 未设置OPENAI_API_KEY环境变量');
      console.log('💡 请在.env文件中设置您的OpenAI API密钥');
      process.exit(1);
    }

    // 2. 测试API连接
    console.log('\n🔗 测试API连接...');
    const connectionSuccess = await openaiConfig.testConnection();

    if (!connectionSuccess) {
      console.log('\n❌ API连接测试失败');
      console.log('💡 请检查以下配置:');
      console.log('   - OPENAI_API_KEY是否正确');
      console.log('   - OPENAI_BASE_URL是否可访问');
      console.log('   - 网络连接是否正常');
      process.exit(1);
    }

    // 3. 测试聊天完成API
    console.log('\n💬 测试聊天完成API...');
    await testChatCompletion();

    // 4. 测试嵌入API
    console.log('\n🔍 测试嵌入API...');
    await testEmbedding();

    console.log('\n✅ 所有测试通过！OpenAI配置正确。');
    console.log('\n🚀 您现在可以启动翻译服务了:');
    console.log('   node start-services.js');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);

    // 提供详细的错误诊断
    if (error.message.includes('401')) {
      console.log('\n💡 诊断建议:');
      console.log('   - 检查OPENAI_API_KEY是否正确');
      console.log('   - 确认API密钥是否有效且未过期');
    } else if (error.message.includes('timeout') || error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 诊断建议:');
      console.log('   - 检查网络连接');
      console.log('   - 验证OPENAI_BASE_URL是否正确');
      console.log('   - 如果使用代理，确认代理服务正常');
    } else if (error.message.includes('rate_limit')) {
      console.log('\n💡 诊断建议:');
      console.log('   - API调用频率过高，请稍后重试');
      console.log('   - 检查账户配额是否充足');
    }

    process.exit(1);
  }
}

async function testChatCompletion() {
  try {
    const OpenAI = require('openai');
    const client = new OpenAI(openaiConfig.getClientConfig());
    const chatDefaults = openaiConfig.getChatCompletionDefaults();

    const response = await openaiConfig.withRetry(async () => {
      return await client.chat.completions.create({
        ...chatDefaults,
        max_tokens: 50,
        messages: [
          {
            role: "user",
            content: "请用中文回答：你好，这是一个API测试。"
          }
        ]
      });
    });

    if (response && response.choices && response.choices[0]) {
      console.log('   ✅ 聊天完成API测试成功');
      console.log(`   📝 响应: ${response.choices[0].message.content.trim()}`);
    } else {
      throw new Error('聊天完成API返回格式异常');
    }

  } catch (error) {
    console.log('   ❌ 聊天完成API测试失败');
    throw error;
  }
}

async function testEmbedding() {
  try {
    const { OpenAIEmbeddings } = require('@langchain/openai');
    const embeddings = new OpenAIEmbeddings(openaiConfig.getEmbeddingsConfig());

    const testText = "这是一个嵌入API测试文本";
    const result = await embeddings.embedQuery(testText);

    if (result && Array.isArray(result) && result.length > 0) {
      console.log('   ✅ 嵌入API测试成功');
      console.log(`   📊 向量维度: ${result.length}`);
    } else {
      throw new Error('嵌入API返回格式异常');
    }

  } catch (error) {
    console.log('   ❌ 嵌入API测试失败');
    throw error;
  }
}

// 显示使用帮助
function showHelp() {
  console.log(`
使用方法:
  node test-openai-config.js

环境变量配置:
  OPENAI_API_KEY      - OpenAI API密钥 (必需)
  OPENAI_BASE_URL     - OpenAI API基础URL (可选)
  OPENAI_MODEL        - 聊天模型名称 (可选，默认: gpt-4)
  OPENAI_TEMPERATURE  - 温度参数 (可选，默认: 0.3)

示例配置 (.env文件):
  OPENAI_API_KEY=sk-your-api-key-here
  OPENAI_BASE_URL=https://api.openai.com/v1
  OPENAI_MODEL=gpt-4
  OPENAI_TEMPERATURE=0.3

常见Base URL示例:
  # 官方API
  OPENAI_BASE_URL=https://api.openai.com/v1

  # 代理服务示例
  OPENAI_BASE_URL=https://your-proxy-domain.com/v1
  OPENAI_BASE_URL=https://api.openai-proxy.com/v1

  # Azure OpenAI
  OPENAI_BASE_URL=https://your-resource.openai.azure.com/openai/deployments/your-deployment
`);
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// 运行测试
if (require.main === module) {
  testOpenAIConfiguration().catch(error => {
    console.error('测试过程中发生未预期的错误:', error);
    process.exit(1);
  });
}

module.exports = {
  testOpenAIConfiguration,
  testChatCompletion,
  testEmbedding
};
