import type { protos } from "@google-ai/generativelanguage";
import type { google } from "@google-ai/generativelanguage/build/protos/protos.js";
import { GoogleAuthOptions } from "google-auth-library";
import type { BaseLanguageModel } from "@langchain/core/language_models/base";
import { Runnable } from "@langchain/core/runnables";
import { PromptTemplate } from "@langchain/core/prompts";
import { AsyncCaller, AsyncCallerCallOptions } from "@langchain/core/utils/async_caller";
import { GoogleAbstractedClientOpsMethod, GoogleResponse, GoogleVertexAIConnectionParams } from "../../../types/googlevertexai-types.js";
import { GoogleConnection } from "../../../util/googlevertexai-connection.js";
/**
 * Configuration that allows us to load or pull a prompt that has been created
 * by the Google MakerSuite site and saved in Google Drive.
 *
 * There is a simple in-memory cache of these prompts that is refreshed
 * after the cacheTimeout specified in the configuration.
 */
export interface MakerSuiteHubConfig {
    /**
     * How long, in milliseconds, before a prompt is assumed stale and should
     * be refreshed from the copy in Google Drive.
     */
    cacheTimeout: number;
    caller?: AsyncCaller;
}
type MakerSuitePromptType = "text" | "data" | "chat";
export interface MakerSuitePromptVariable {
    variableId: string;
    displayName: string;
}
export interface MakerSuiteRunSettings {
    temperature?: number;
    model: string;
    candidateCount?: number;
    topP?: number;
    topK?: number;
    maxOutputTokens: number;
    safetySettings?: protos.google.ai.generativelanguage.v1beta2.ISafetySetting[];
}
export interface MakerSuiteTextPromptData {
    textPrompt: {
        value?: string;
        variables?: MakerSuitePromptVariable[];
    };
    runSettings?: MakerSuiteRunSettings;
    testExamples?: unknown;
}
export interface MakerSuiteDataPromptColumn {
    columnId: string;
    displayName: string;
    isInput?: boolean;
}
export interface MakerSuiteDataPromptRow {
    rowId: string;
    columnBindings: Record<string, string>;
}
export interface MakerSuiteDataPromptData {
    dataPrompt: {
        preamble: string;
        columns: MakerSuiteDataPromptColumn[];
        rows: MakerSuiteDataPromptRow[];
        rowsUsed: string[];
    };
    runSettings?: MakerSuiteRunSettings;
    testExamples?: unknown;
}
export interface MakerSuiteChatExchange {
    request?: string;
    response?: string;
    source: string;
    id: string;
}
export interface MakerSuiteChatPromptData {
    multiturnPrompt: {
        preamble: string;
        primingExchanges: MakerSuiteChatExchange[];
        sessions: {
            sessionExchanges: MakerSuiteChatExchange[];
        }[];
    };
    runSettings?: MakerSuiteRunSettings;
}
/**
 * These are the possible formats that the JSON generated by MakerSuite
 * and saved in Google Drive can be.
 */
export type MakerSuitePromptData = MakerSuiteTextPromptData | MakerSuiteDataPromptData | MakerSuiteChatPromptData;
/**
 * A class that represents the Prompt that has been created by MakerSuite
 * and loaded from Google Drive. It exposes methods to turn this prompt
 * into a Template, a Model, and into a chain consisting of these two elements.
 * In general, this class should be created by the MakerSuiteHub class and
 * not instantiated manually.
 */
export declare class MakerSuitePrompt {
    promptType: MakerSuitePromptType;
    promptData: MakerSuitePromptData;
    constructor(promptData: MakerSuitePromptData);
    _determinePromptType(): void;
    _promptValueText(): string;
    _promptValueData(): string;
    _promptValueChat(): string;
    _promptValue(): string;
    /**
     * Create a template from the prompt, including any "test input" specified
     * in MakerSuite as a template parameter.
     */
    toTemplate(): PromptTemplate;
    _modelName(): string;
    _examples(): google.ai.generativelanguage.v1beta2.IExample[];
    /**
     * Create a model from the prompt with all the parameters (model name,
     * temperature, etc) set as they were in MakerSuite.
     */
    toModel(): BaseLanguageModel;
    /**
     * Create a RunnableSequence based on the template and model that can
     * be created from this prompt. The template will have parameters available
     * based on the "test input" that was set in MakerSuite, and the model
     * will have the parameters (model name, temperature, etc) from those in
     * MakerSuite.
     */
    toChain(): Runnable<any, any, import("@langchain/core/runnables").RunnableConfig>;
}
interface DriveFileReadParams extends GoogleVertexAIConnectionParams<GoogleAuthOptions> {
    fileId: string;
}
interface DriveCallOptions extends AsyncCallerCallOptions {
}
interface DriveFileMakerSuiteResponse extends GoogleResponse {
    data: MakerSuitePromptData;
}
export declare class DriveFileReadConnection extends GoogleConnection<DriveCallOptions, DriveFileMakerSuiteResponse> implements DriveFileReadParams {
    endpoint: string;
    apiVersion: string;
    fileId: string;
    constructor(fields: DriveFileReadParams, caller: AsyncCaller);
    buildUrl(): Promise<string>;
    buildMethod(): GoogleAbstractedClientOpsMethod;
    request(options?: DriveCallOptions): Promise<DriveFileMakerSuiteResponse>;
}
export interface CacheEntry {
    updated: number;
    prompt: MakerSuitePrompt;
}
/**
 * A class allowing access to MakerSuite prompts that have been saved in
 * Google Drive.
 * MakerSuite prompts are pulled based on their Google Drive ID (which is available
 * from Google Drive or as part of the URL when the prompt is open in MakerSuite).
 * There is a basic cache that will store the prompt in memory for a time specified
 * in milliseconds. This defaults to 0, indicating the prompt should always be
 * pulled from Google Drive.
 */
export declare class MakerSuiteHub {
    cache: Record<string, CacheEntry>;
    cacheTimeout: number;
    caller: AsyncCaller;
    constructor(config?: MakerSuiteHubConfig);
    /**
     * Is the current cache entry valid, or does it need to be refreshed.
     * It will need to be refreshed under any of the following conditions:
     * - It does not currently exist in the cache
     * - The cacheTimeout is 0
     * - The cache was last updated longer ago than the cacheTimeout allows
     * @param entry - The cache entry, including when this prompt was last refreshed
     */
    isValid(entry: CacheEntry): boolean;
    /**
     * Get a MakerSuitePrompt that is specified by the Google Drive ID.
     * This will always be loaded from Google Drive.
     * @param id
     * @return A MakerSuitePrompt which can be used to create a template, model, or chain.
     */
    forcePull(id: string): Promise<MakerSuitePrompt>;
    /**
     * Get a MakerSuitePrompt that is specified by the Google Drive ID. This may be
     * loaded from Google Drive or, if there is a valid copy in the cache, the cached
     * copy will be returned.
     * @param id
     * @return A MakerSuitePrompt which can be used to create a template, model, or chain.
     */
    pull(id: string): Promise<MakerSuitePrompt>;
}
export {};
