#!/usr/bin/env node

/**
 * 测试修复后的服务启动
 */

console.log('🧪 测试服务修复...');

try {
  // 测试导入各个服务（不应该启动服务器）
  console.log('📦 测试服务导入...');
  
  console.log('   导入 PineCone 服务...');
  const { PineconeTranslationService } = require('./pinecone-service');
  console.log('   ✅ PineCone 服务导入成功');
  
  console.log('   导入多智能体翻译服务...');
  const { MultiAgentTranslator } = require('./multi-agent-translator');
  console.log('   ✅ 多智能体翻译服务导入成功');
  
  console.log('   导入文档处理服务...');
  const { DocumentProcessor } = require('./document-processor');
  console.log('   ✅ 文档处理服务导入成功');
  
  console.log('   导入PDF提取服务...');
  const pdfApp = require('./pdf-extraction-service');
  console.log('   ✅ PDF提取服务导入成功');
  
  console.log('   导入主服务...');
  const TranslationWorkflowManager = require('./index');
  console.log('   ✅ 主服务导入成功');
  
  console.log('\n🎉 所有服务导入测试通过！');
  console.log('💡 现在可以安全地运行 node start-services.js');
  
  // 测试实例化
  console.log('\n🔧 测试服务实例化...');
  
  const pineconeService = new PineconeTranslationService();
  console.log('   ✅ PineCone 服务实例化成功');
  
  const translator = new MultiAgentTranslator();
  console.log('   ✅ 多智能体翻译服务实例化成功');
  
  const processor = new DocumentProcessor();
  console.log('   ✅ 文档处理服务实例化成功');
  
  const manager = new TranslationWorkflowManager();
  console.log('   ✅ 主服务实例化成功');
  
  console.log('\n✅ 所有测试通过！服务修复成功。');
  
} catch (error) {
  console.error('\n❌ 测试失败:', error.message);
  console.error('\n🔍 错误详情:', error.stack);
  process.exit(1);
}
