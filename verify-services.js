#!/usr/bin/env node

const http = require('http');

// 服务配置
const services = [
  { name: 'PDF提取服务', port: 3011, path: '/health' },
  { name: 'PineCone服务', port: 3012, path: '/' },
  { name: '多智能体翻译服务', port: 3013, path: '/' },
  { name: '文档处理服务', port: 3014, path: '/health' },
  { name: '主服务', port: 3000, path: '/health' }
];

// 测试单个服务
function testService(service) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: service.port,
      path: service.path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`✅ ${service.name} (端口 ${service.port}) - 正常运行`);
          resolve({ service: service.name, status: 'success', port: service.port });
        } else {
          console.log(`⚠️  ${service.name} (端口 ${service.port}) - 响应异常 (状态码: ${res.statusCode})`);
          resolve({ service: service.name, status: 'warning', port: service.port, statusCode: res.statusCode });
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ ${service.name} (端口 ${service.port}) - 连接失败: ${error.message}`);
      resolve({ service: service.name, status: 'error', port: service.port, error: error.message });
    });

    req.on('timeout', () => {
      console.log(`⏰ ${service.name} (端口 ${service.port}) - 连接超时`);
      req.destroy();
      resolve({ service: service.name, status: 'timeout', port: service.port });
    });

    req.end();
  });
}

// 主验证函数
async function verifyAllServices() {
  console.log('🔍 验证所有翻译服务状态...\n');
  
  const results = [];
  
  for (const service of services) {
    const result = await testService(service);
    results.push(result);
  }
  
  console.log('\n📊 验证结果汇总:');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.status === 'success');
  const warnings = results.filter(r => r.status === 'warning');
  const errors = results.filter(r => r.status === 'error' || r.status === 'timeout');
  
  console.log(`✅ 正常运行: ${successful.length}/${services.length}`);
  console.log(`⚠️  异常响应: ${warnings.length}/${services.length}`);
  console.log(`❌ 连接失败: ${errors.length}/${services.length}`);
  
  if (successful.length === services.length) {
    console.log('\n🎉 所有服务运行正常！');
    console.log('\n🌐 可以访问以下地址:');
    console.log('   Web界面: http://localhost:3000');
    console.log('   健康检查: http://localhost:3000/health');
    console.log('   API状态: http://localhost:3000/api/workflow/status');
  } else {
    console.log('\n⚠️  部分服务存在问题，请检查:');
    
    if (warnings.length > 0) {
      console.log('\n异常响应的服务:');
      warnings.forEach(w => {
        console.log(`   - ${w.service} (端口 ${w.port}): 状态码 ${w.statusCode}`);
      });
    }
    
    if (errors.length > 0) {
      console.log('\n连接失败的服务:');
      errors.forEach(e => {
        console.log(`   - ${e.service} (端口 ${e.port}): ${e.error || '超时'}`);
      });
      
      console.log('\n💡 解决建议:');
      console.log('   1. 确认服务已启动: npm start');
      console.log('   2. 检查端口占用: npm run cleanup');
      console.log('   3. 查看服务日志获取详细错误信息');
    }
  }
  
  console.log('\n📝 详细结果:');
  results.forEach(result => {
    const status = result.status === 'success' ? '✅' : 
                   result.status === 'warning' ? '⚠️' : '❌';
    console.log(`   ${status} ${result.service} - 端口 ${result.port}`);
  });
}

// 运行验证
if (require.main === module) {
  verifyAllServices().catch(error => {
    console.error('验证过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = { verifyAllServices, testService };
