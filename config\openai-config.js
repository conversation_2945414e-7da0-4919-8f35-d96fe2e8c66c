require('dotenv').config();

/**
 * OpenAI配置管理模块
 * 统一处理OpenAI API的配置，支持自定义base URL
 */
class OpenAIConfig {
  constructor() {
    this.validateConfig();
  }

  /**
   * 验证必要的配置项
   */
  validateConfig() {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY环境变量未设置');
    }
  }

  /**
   * 获取OpenAI客户端配置
   * @returns {Object} OpenAI客户端配置对象
   */
  getClientConfig() {
    const config = {
      apiKey: process.env.OPENAI_API_KEY
    };

    // 如果设置了自定义base URL，则添加配置
    if (process.env.OPENAI_BASE_URL) {
      config.baseURL = process.env.OPENAI_BASE_URL;
      console.log(`使用自定义OpenAI Base URL: ${process.env.OPENAI_BASE_URL}`);
    } else {
      console.log('使用默认OpenAI API地址');
    }

    return config;
  }

  /**
   * 获取LangChain OpenAI Embeddings配置
   * @param {string} modelName - 模型名称，默认为text-embedding-ada-002
   * @returns {Object} LangChain OpenAI Embeddings配置对象
   */
  getEmbeddingsConfig(modelName = 'text-embedding-ada-002') {
    const config = {
      openAIApiKey: process.env.OPENAI_API_KEY,
      modelName: modelName
    };

    // 如果设置了自定义base URL，则添加配置
    if (process.env.OPENAI_BASE_URL) {
      config.configuration = {
        baseURL: process.env.OPENAI_BASE_URL
      };
    }

    return config;
  }

  /**
   * 获取聊天完成的默认参数
   * @returns {Object} 聊天完成的默认参数
   */
  getChatCompletionDefaults() {
    return {
      model: process.env.OPENAI_MODEL || "gpt-4",
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.3,
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS) || 2000,
      top_p: parseFloat(process.env.OPENAI_TOP_P) || 1,
      frequency_penalty: parseFloat(process.env.OPENAI_FREQUENCY_PENALTY) || 0,
      presence_penalty: parseFloat(process.env.OPENAI_PRESENCE_PENALTY) || 0
    };
  }

  /**
   * 获取嵌入模型的默认参数
   * @returns {Object} 嵌入模型的默认参数
   */
  getEmbeddingDefaults() {
    return {
      model: process.env.OPENAI_EMBEDDING_MODEL || "text-embedding-ada-002",
      encoding_format: "float"
    };
  }

  /**
   * 检查API连接状态
   * @returns {Promise<boolean>} 连接是否成功
   */
  async testConnection() {
    try {
      const OpenAI = require('openai');
      const client = new OpenAI(this.getClientConfig());
      
      // 尝试获取模型列表来测试连接
      const response = await client.models.list();
      
      if (response && response.data) {
        console.log('✅ OpenAI API连接测试成功');
        console.log(`可用模型数量: ${response.data.length}`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('❌ OpenAI API连接测试失败:', error.message);
      
      // 提供详细的错误信息和解决建议
      if (error.message.includes('401')) {
        console.error('💡 建议: 请检查OPENAI_API_KEY是否正确');
      } else if (error.message.includes('timeout') || error.message.includes('ECONNREFUSED')) {
        console.error('💡 建议: 请检查网络连接或OPENAI_BASE_URL配置');
      }
      
      return false;
    }
  }

  /**
   * 获取当前配置信息（用于调试）
   * @returns {Object} 当前配置信息（不包含敏感信息）
   */
  getConfigInfo() {
    return {
      hasApiKey: !!process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
      model: process.env.OPENAI_MODEL || 'gpt-4',
      embeddingModel: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-ada-002',
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.3,
      maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS) || 2000
    };
  }

  /**
   * 创建带有重试机制的API调用包装器
   * @param {Function} apiCall - API调用函数
   * @param {number} maxRetries - 最大重试次数
   * @param {number} retryDelay - 重试延迟（毫秒）
   * @returns {Promise} API调用结果
   */
  async withRetry(apiCall, maxRetries = 3, retryDelay = 1000) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error;
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === maxRetries) {
          throw error;
        }
        
        // 检查是否是可重试的错误
        if (this.isRetryableError(error)) {
          console.log(`API调用失败，第${attempt}次重试 (${retryDelay}ms后): ${error.message}`);
          await this.delay(retryDelay);
          retryDelay *= 2; // 指数退避
        } else {
          // 不可重试的错误，直接抛出
          throw error;
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 判断错误是否可重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否可重试
   */
  isRetryableError(error) {
    const retryableErrors = [
      'timeout',
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'rate_limit_exceeded',
      'server_error'
    ];
    
    const errorMessage = error.message.toLowerCase();
    return retryableErrors.some(retryableError => 
      errorMessage.includes(retryableError)
    );
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 创建单例实例
const openaiConfig = new OpenAIConfig();

module.exports = openaiConfig;
