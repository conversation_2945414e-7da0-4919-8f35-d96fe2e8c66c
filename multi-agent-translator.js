const express = require('express');
const OpenAI = require('openai');
const openaiConfig = require('./config/openai-config');

class MultiAgentTranslator {
  constructor() {
    // 使用统一的OpenAI配置
    this.openai = new OpenAI(openaiConfig.getClientConfig());

    // 定义智能体角色
    this.agents = {
      translator: {
        name: "专业翻译员",
        systemPrompt: `你是一位专业的英文-繁体中文翻译专家。你的任务是：
1. 严格按照提供的参考文档和术语对照表进行翻译
2. 优先使用参考文档中的现有译法，确保术语一致性
3. 保持原文的格式、结构和语气
4. 对于专业术语，必须使用参考资料中的标准译法
5. 如果遇到参考资料中没有的内容，采用最贴近的专业翻译标准
6. 翻译结果必须自然流畅，符合目标语言的表达习惯`
      },

      reviewer: {
        name: "翻译校对员",
        systemPrompt: `你是一位严格的翻译质量校对专家。你的任务是：
1. 检查翻译是否严格遵循了参考文档的译法
2. 验证术语翻译的一致性和准确性
3. 检查格式是否保持完整
4. 确保翻译的自然度和可读性
5. 标出任何偏离参考标准的地方
6. 提供具体的修改建议
7. 给出翻译质量评分（1-10分）`
      },

      termChecker: {
        name: "术语一致性检查员",
        systemPrompt: `你是专门负责术语一致性检查的专家。你的任务是：
1. 识别文本中的所有专业术语
2. 检查这些术语是否在参考资料中有对应译法
3. 验证术语翻译在整个文档中的一致性
4. 标记任何术语翻译不一致的地方
5. 提供术语标准化建议`
      }
    };
  }

  // 翻译文本段落
  async translateSegment(segment, referenceData, direction) {
    try {
      const { translationMap, termMap } = referenceData;

      // 第一步：专业翻译员翻译
      const translation = await this.performTranslation(segment, translationMap, termMap, direction);

      // 第二步：术语检查员检查
      const termCheck = await this.checkTermConsistency(segment, translation, termMap, direction);

      // 第三步：校对员审核
      const review = await this.reviewTranslation(segment, translation, referenceData, direction);

      // 第四步：如果需要，进行修正
      let finalTranslation = translation;
      if (review.needsRevision) {
        finalTranslation = await this.reviseTranslation(segment, translation, review.suggestions, referenceData, direction);
      }

      return {
        original: segment,
        translation: finalTranslation,
        quality: {
          translatorOutput: translation,
          termCheck: termCheck,
          review: review,
          finalScore: review.score
        }
      };

    } catch (error) {
      console.error('翻译段落失败:', error);
      throw error;
    }
  }

  // 执行翻译
  async performTranslation(text, translationMap, termMap, direction) {
    const isEnglishToChineseDirection = direction === '英文 → 繁体中文';
    const sourceLanguage = isEnglishToChineseDirection ? '英文' : '繁体中文';
    const targetLanguage = isEnglishToChineseDirection ? '繁体中文' : '英文';

    // 构建参考信息
    let referenceInfo = "参考译法：\n";

    // 查找相似的翻译对
    const similarTranslations = this.findSimilarTranslations(text, translationMap);
    if (similarTranslations.length > 0) {
      referenceInfo += "相似段落译法：\n";
      similarTranslations.forEach((pair, index) => {
        referenceInfo += `${index + 1}. ${pair.source} → ${pair.target}\n`;
      });
    }

    // 添加术语对照
    if (Object.keys(termMap).length > 0) {
      referenceInfo += "\n术语对照表：\n";
      Object.entries(termMap).forEach(([source, target]) => {
        if (isEnglishToChineseDirection) {
          referenceInfo += `${source} → ${target}\n`;
        } else {
          referenceInfo += `${target} → ${source}\n`;
        }
      });
    }

    const prompt = `请将以下${sourceLanguage}文本翻译成${targetLanguage}。

${referenceInfo}

要翻译的文本：
${text}

要求：
1. 严格按照参考译法进行翻译
2. 确保术语使用的一致性
3. 保持原文格式和结构
4. 翻译要自然流畅

翻译结果：`;

    const chatDefaults = openaiConfig.getChatCompletionDefaults();
    const response = await openaiConfig.withRetry(async () => {
      return await this.openai.chat.completions.create({
        ...chatDefaults,
        messages: [
          { role: "system", content: this.agents.translator.systemPrompt },
          { role: "user", content: prompt }
        ]
      });
    });

    return response.choices[0].message.content.trim();
  }

  // 检查术语一致性
  async checkTermConsistency(original, translation, termMap, direction) {
    const isEnglishToChinese = direction === '英文 → 繁体中文';

    const prompt = `请检查以下翻译中的术语一致性：

原文：${original}
译文：${translation}

术语对照表：
${Object.entries(termMap).map(([en, zh]) =>
      isEnglishToChinese ? `${en} → ${zh}` : `${zh} → ${en}`
    ).join('\n')}

请检查：
1. 是否所有术语都按照对照表翻译
2. 是否有遗漏或错误的术语翻译
3. 给出具体的问题和建议

检查结果：`;

    const chatDefaults = openaiConfig.getChatCompletionDefaults();
    const response = await openaiConfig.withRetry(async () => {
      return await this.openai.chat.completions.create({
        ...chatDefaults,
        temperature: 0.1,
        max_tokens: 1000,
        messages: [
          { role: "system", content: this.agents.termChecker.systemPrompt },
          { role: "user", content: prompt }
        ]
      });
    });

    return {
      check: response.choices[0].message.content.trim(),
      passed: !response.choices[0].message.content.includes('问题') &&
        !response.choices[0].message.content.includes('错误')
    };
  }

  // 审核翻译质量
  async reviewTranslation(original, translation, referenceData, direction) {
    const prompt = `请审核以下翻译的质量：

原文：${original}
译文：${translation}

请从以下方面评估：
1. 是否遵循了参考文档的译法
2. 术语翻译是否准确一致
3. 格式是否保持完整
4. 语言是否自然流畅
5. 整体翻译质量

请给出：
- 质量评分（1-10分）
- 具体问题（如有）
- 修改建议（如需要）
- 是否需要修正（是/否）

评审结果：`;

    const chatDefaults = openaiConfig.getChatCompletionDefaults();
    const response = await openaiConfig.withRetry(async () => {
      return await this.openai.chat.completions.create({
        ...chatDefaults,
        temperature: 0.2,
        max_tokens: 1000,
        messages: [
          { role: "system", content: this.agents.reviewer.systemPrompt },
          { role: "user", content: prompt }
        ]
      });
    });

    const reviewText = response.choices[0].message.content.trim();

    return {
      review: reviewText,
      score: this.extractScore(reviewText),
      needsRevision: reviewText.includes('需要修正：是') || reviewText.includes('需要修改'),
      suggestions: this.extractSuggestions(reviewText)
    };
  }

  // 修正翻译
  async reviseTranslation(original, translation, suggestions, referenceData, direction) {
    const prompt = `请根据以下建议修正翻译：

原文：${original}
当前译文：${translation}

修改建议：
${suggestions}

请提供修正后的翻译：`;

    const chatDefaults = openaiConfig.getChatCompletionDefaults();
    const response = await openaiConfig.withRetry(async () => {
      return await this.openai.chat.completions.create({
        ...chatDefaults,
        temperature: 0.2,
        max_tokens: 1500,
        messages: [
          { role: "system", content: this.agents.translator.systemPrompt },
          { role: "user", content: prompt }
        ]
      });
    });

    return response.choices[0].message.content.trim();
  }

  // 查找相似翻译
  findSimilarTranslations(text, translationMap, maxResults = 3) {
    const similarities = [];

    for (const [source, target] of Object.entries(translationMap)) {
      const similarity = this.calculateSimilarity(text, source);
      if (similarity > 0.3) {
        similarities.push({ source, target, similarity });
      }
    }

    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, maxResults);
  }

  // 计算文本相似度（简单实现）
  calculateSimilarity(text1, text2) {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);

    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];

    return intersection.length / union.length;
  }

  // 提取评分
  extractScore(reviewText) {
    const scoreMatch = reviewText.match(/(\d+)分/);
    return scoreMatch ? parseInt(scoreMatch[1]) : 7;
  }

  // 提取建议
  extractSuggestions(reviewText) {
    const lines = reviewText.split('\n');
    const suggestions = lines.filter(line =>
      line.includes('建议') || line.includes('修改') || line.includes('问题')
    );
    return suggestions.join('\n');
  }
}

// Express服务器
const app = express();
const port = 3003;

app.use(express.json({ limit: '10mb' }));

const translator = new MultiAgentTranslator();

// 翻译段落
app.post('/api/translate-segment', async (req, res) => {
  try {
    const { segment, referenceData, direction } = req.body;

    const result = await translator.translateSegment(segment, referenceData, direction);

    res.json({
      success: true,
      result: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 批量翻译
app.post('/api/translate-batch', async (req, res) => {
  try {
    const { segments, referenceData, direction } = req.body;

    const results = [];
    for (const segment of segments) {
      const result = await translator.translateSegment(segment, referenceData, direction);
      results.push(result);
    }

    res.json({
      success: true,
      results: results,
      totalSegments: segments.length,
      averageScore: results.reduce((sum, r) => sum + r.quality.finalScore, 0) / results.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

app.listen(port, () => {
  console.log(`多智能体翻译服务运行在端口 ${port}`);
});

module.exports = { MultiAgentTranslator, app };
