#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔥 N8N精准文档翻译工作流');
console.log('=====================================');
console.log('版本: 1.0.0');
console.log('作者: AI Assistant');
console.log('=====================================\n');

// 检查环境
console.log('🔍 检查环境配置...');

if (!fs.existsSync('.env')) {
  console.log('❌ 未找到.env文件，请复制.env.example并配置API密钥');
  process.exit(1);
}

// 确保目录存在
const dirs = ['uploads', 'outputs', 'logs'];
for (const dir of dirs) {
  if (!fs.existsSync(dir)) {
    console.log(`📁 创建目录: ${dir}`);
    fs.mkdirSync(dir, { recursive: true });
  }
}

console.log('✅ 环境检查完成\n');

// 服务配置
const services = [
  {
    name: 'PDF提取服务',
    script: 'pdf-extraction-service.js',
    port: 3001
  },
  {
    name: 'PineCone服务',
    script: 'pinecone-service.js',
    port: 3002
  },
  {
    name: '多智能体翻译服务',
    script: 'multi-agent-translator.js',
    port: 3003
  },
  {
    name: '文档处理服务',
    script: 'document-processor.js',
    port: 3004
  },
  {
    name: '主服务',
    script: 'index.js',
    port: 3000
  }
];

const runningProcesses = [];

// 启动单个服务
function startService(service) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 启动 ${service.name} (端口 ${service.port})...`);
    
    const childProcess = spawn('node', [service.script], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, PORT: service.port }
    });

    runningProcesses.push({
      name: service.name,
      process: childProcess,
      port: service.port
    });

    // 处理输出
    childProcess.stdout.on('data', (data) => {
      console.log(`[${service.name}] ${data.toString().trim()}`);
    });

    childProcess.stderr.on('data', (data) => {
      console.error(`[${service.name}] ERROR: ${data.toString().trim()}`);
    });

    // 处理进程退出
    childProcess.on('exit', (code) => {
      console.log(`❌ ${service.name} 退出 (代码: ${code})`);
      if (code !== 0) {
        reject(new Error(`${service.name} 启动失败`));
      }
    });

    // 等待服务启动
    setTimeout(() => {
      if (childProcess.pid) {
        console.log(`✅ ${service.name} 启动成功 (PID: ${childProcess.pid})`);
        resolve();
      } else {
        reject(new Error(`${service.name} 启动超时`));
      }
    }, 3000);
  });
}

// 停止所有服务
function stopAllServices() {
  console.log('\n🛑 正在停止所有服务...');
  
  runningProcesses.forEach(service => {
    if (service.process && service.process.pid) {
      console.log(`   停止 ${service.name}...`);
      try {
        service.process.kill('SIGTERM');
      } catch (error) {
        console.error(`   停止 ${service.name} 时出错: ${error.message}`);
      }
    }
  });
  
  console.log('✅ 所有服务已停止');
  process.exit(0);
}

// 设置信号处理
process.on('SIGINT', () => {
  console.log('\n收到停止信号...');
  stopAllServices();
});

process.on('SIGTERM', () => {
  console.log('\n收到终止信号...');
  stopAllServices();
});

// 主函数
async function main() {
  try {
    console.log('🎯 启动所有翻译服务...\n');
    
    // 按顺序启动服务
    for (const service of services) {
      await startService(service);
      await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
    }
    
    console.log('\n🎉 所有服务启动完成！');
    console.log('\n📋 服务状态:');
    services.forEach(service => {
      console.log(`   ${service.name}: http://localhost:${service.port}`);
    });
    
    console.log('\n🌐 访问地址:');
    console.log('   主服务: http://localhost:3000');
    console.log('   健康检查: http://localhost:3000/health');
    console.log('   工作流状态: http://localhost:3000/api/workflow/status');
    
    console.log('\n💡 提示:');
    console.log('   - 按 Ctrl+C 停止所有服务');
    console.log('   - 查看 ./logs/ 目录获取详细日志');
    console.log('   - 导入 n8n-translation-workflow.json 到N8N中使用');
    
    // 保持进程运行
    process.stdin.resume();
    
  } catch (error) {
    console.error(`❌ 服务启动失败: ${error.message}`);
    stopAllServices();
  }
}

// 运行主函数
main().catch(error => {
  console.error('启动失败:', error);
  process.exit(1);
});
