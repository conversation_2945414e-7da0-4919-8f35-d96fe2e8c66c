{"name": "n8n-translation-workflow", "version": "1.0.0", "description": "N8N工作流实现docx文档精准的英文、繁体中文互译", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "start:pdf": "node pdf-extraction-service.js", "start:pinecone": "node pinecone-service.js", "start:translator": "node multi-agent-translator.js", "start:processor": "node document-processor.js", "start:all": "concurrently \"npm run start:pdf\" \"npm run start:pinecone\" \"npm run start:translator\" \"npm run start:processor\"", "test": "jest", "test:openai": "node test-openai-config.js", "lint": "eslint ."}, "keywords": ["n8n", "translation", "document", "ai", "multi-agent", "pinecone", "docx", "pdf"], "author": "AI Assistant", "license": "MIT", "dependencies": {"@langchain/openai": "^0.0.14", "@pinecone-database/pinecone": "^1.1.2", "cors": "^2.8.5", "docx": "^8.5.0", "dotenv": "^16.4.5", "express": "^4.18.2", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "langchain": "^0.1.25", "mammoth": "^1.6.0", "multer": "^1.4.5-lts.1", "openai": "^4.28.4", "path": "^0.12.7", "pdf-parse": "^1.1.1", "uuid": "^9.0.1"}, "devDependencies": {"concurrently": "^8.2.2", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.0.3", "supertest": "^6.3.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/n8n-translation-workflow.git"}, "bugs": {"url": "https://github.com/your-username/n8n-translation-workflow/issues"}, "homepage": "https://github.com/your-username/n8n-translation-workflow#readme"}