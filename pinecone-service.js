const { PineconeClient } = require('@pinecone-database/pinecone');
const { OpenAIEmbeddings } = require('langchain/embeddings/openai');
const express = require('express');
const openaiConfig = require('./config/openai-config');

class PineconeTranslationService {
  constructor() {
    this.pinecone = new PineconeClient();

    // 使用统一的OpenAI配置
    this.embeddings = new OpenAIEmbeddings(openaiConfig.getEmbeddingsConfig());
    this.index = null;
    this.initialized = false;
  }

  async initialize() {
    try {
      await this.pinecone.init({
        environment: process.env.PINECONE_ENVIRONMENT,
        apiKey: process.env.PINECONE_API_KEY,
      });

      this.index = this.pinecone.Index(process.env.PINECONE_INDEX_NAME || 'translation-knowledge');
      this.initialized = true;
      console.log('PineCone服务初始化成功');
    } catch (error) {
      console.error('PineCone初始化失败:', error);
      throw error;
    }
  }

  // 查询相似的翻译对
  async findSimilarTranslations(text, language, topK = 5) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      // 生成查询向量
      const queryVector = await this.embeddings.embedQuery(text);

      // 在PineCone中查询
      const queryRequest = {
        vector: queryVector,
        topK: topK,
        includeMetadata: true,
        filter: {
          source_language: language === 'english' ? 'en' : 'zh-TW'
        }
      };

      const queryResponse = await this.index.query({ queryRequest });

      // 处理查询结果
      const similarTranslations = queryResponse.matches.map(match => ({
        score: match.score,
        sourceText: match.metadata.source_text,
        targetText: match.metadata.target_text,
        sourceLanguage: match.metadata.source_language,
        targetLanguage: match.metadata.target_language,
        domain: match.metadata.domain || 'general',
        context: match.metadata.context || ''
      }));

      return similarTranslations;
    } catch (error) {
      console.error('查询PineCone失败:', error);
      return [];
    }
  }

  // 存储翻译对到知识库
  async storeTranslationPair(sourceText, targetText, sourceLanguage, targetLanguage, metadata = {}) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      // 生成源文本的向量
      const sourceVector = await this.embeddings.embedQuery(sourceText);

      // 创建唯一ID
      const id = `${sourceLanguage}-${targetLanguage}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // 准备元数据
      const vectorMetadata = {
        source_text: sourceText,
        target_text: targetText,
        source_language: sourceLanguage,
        target_language: targetLanguage,
        created_at: new Date().toISOString(),
        ...metadata
      };

      // 存储到PineCone
      await this.index.upsert({
        upsertRequest: {
          vectors: [{
            id: id,
            values: sourceVector,
            metadata: vectorMetadata
          }]
        }
      });

      return { success: true, id: id };
    } catch (error) {
      console.error('存储到PineCone失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 批量存储参考文档的翻译对
  async storeReferenceTranslations(translationMap, sourceLanguage, targetLanguage) {
    const results = [];

    for (const [sourceText, targetText] of Object.entries(translationMap)) {
      if (sourceText.length > 10 && targetText.length > 5) { // 过滤太短的文本
        const result = await this.storeTranslationPair(
          sourceText,
          targetText,
          sourceLanguage,
          targetLanguage,
          {
            type: 'reference_document',
            domain: 'user_provided'
          }
        );
        results.push(result);
      }
    }

    return results;
  }

  // 查找术语翻译
  async findTermTranslation(term, sourceLanguage, targetLanguage) {
    const similarTranslations = await this.findSimilarTranslations(term, sourceLanguage, 3);

    // 寻找精确匹配或高相似度的术语
    for (const translation of similarTranslations) {
      if (translation.score > 0.9 ||
        translation.sourceText.toLowerCase().includes(term.toLowerCase())) {
        return {
          term: term,
          translation: translation.targetText,
          confidence: translation.score,
          context: translation.context
        };
      }
    }

    return null;
  }
}

// Express服务器
const app = express();
const port = 3002;

app.use(express.json());

const pineconeService = new PineconeTranslationService();

// 查询相似翻译
app.post('/api/find-similar', async (req, res) => {
  try {
    const { text, language, topK = 5 } = req.body;

    if (!text || !language) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const results = await pineconeService.findSimilarTranslations(text, language, topK);

    res.json({
      success: true,
      query: text,
      language: language,
      results: results
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 存储翻译对
app.post('/api/store-translation', async (req, res) => {
  try {
    const { sourceText, targetText, sourceLanguage, targetLanguage, metadata } = req.body;

    const result = await pineconeService.storeTranslationPair(
      sourceText,
      targetText,
      sourceLanguage,
      targetLanguage,
      metadata
    );

    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 批量存储参考文档
app.post('/api/store-reference', async (req, res) => {
  try {
    const { translationMap, sourceLanguage, targetLanguage } = req.body;

    const results = await pineconeService.storeReferenceTranslations(
      translationMap,
      sourceLanguage,
      targetLanguage
    );

    res.json({
      success: true,
      stored: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results: results
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 查找术语翻译
app.post('/api/find-term', async (req, res) => {
  try {
    const { term, sourceLanguage, targetLanguage } = req.body;

    const result = await pineconeService.findTermTranslation(term, sourceLanguage, targetLanguage);

    res.json({
      success: true,
      term: term,
      translation: result
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.listen(port, () => {
  console.log(`PineCone服务运行在端口 ${port}`);
});

module.exports = { PineconeTranslationService, app };
