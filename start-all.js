#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');

console.log('🔥 N8N精准文档翻译工作流');
console.log('=====================================');
console.log('版本: 1.0.0');
console.log('作者: AI Assistant');
console.log('=====================================\n');

// 检查环境
console.log('🔍 检查环境配置...');

if (!fs.existsSync('.env')) {
  console.log('❌ 未找到.env文件，请复制.env.example并配置API密钥');
  process.exit(1);
}

// 确保目录存在
const dirs = ['uploads', 'outputs', 'logs'];
for (const dir of dirs) {
  if (!fs.existsSync(dir)) {
    console.log(`📁 创建目录: ${dir}`);
    fs.mkdirSync(dir, { recursive: true });
  }
}

console.log('✅ 环境检查完成\n');

// 服务配置 - 使用不同的端口避免冲突
const services = [
  {
    name: 'PDF提取服务',
    script: 'pdf-extraction-service.js',
    port: 3011,
    env: { PDF_SERVICE_PORT: '3011' }
  },
  {
    name: 'PineCone服务',
    script: 'pinecone-service.js',
    port: 3012,
    env: { PINECONE_SERVICE_PORT: '3012' }
  },
  {
    name: '多智能体翻译服务',
    script: 'multi-agent-translator.js',
    port: 3013,
    env: { TRANSLATOR_SERVICE_PORT: '3013' }
  },
  {
    name: '文档处理服务',
    script: 'document-processor.js',
    port: 3014,
    env: { PROCESSOR_SERVICE_PORT: '3014' }
  },
  {
    name: '主服务',
    script: 'index.js',
    port: 3000,
    env: { PORT: '3000' }
  }
];

const runningProcesses = [];

// 启动单个服务
function startService(service) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 启动 ${service.name} (端口 ${service.port})...`);
    
    const env = { ...process.env, ...service.env };
    
    const childProcess = spawn('node', [service.script], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: env
    });

    runningProcesses.push({
      name: service.name,
      process: childProcess,
      port: service.port
    });

    let hasStarted = false;

    // 处理输出
    childProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      console.log(`[${service.name}] ${output}`);
      
      // 检查服务是否启动成功
      if (output.includes('运行在端口') && !hasStarted) {
        hasStarted = true;
        console.log(`✅ ${service.name} 启动成功 (PID: ${childProcess.pid})`);
        resolve();
      }
    });

    childProcess.stderr.on('data', (data) => {
      const error = data.toString().trim();
      console.error(`[${service.name}] ERROR: ${error}`);
      
      // 如果是端口占用错误，尝试使用下一个端口
      if (error.includes('EADDRINUSE')) {
        console.log(`   端口 ${service.port} 被占用，服务可能已在运行`);
        if (!hasStarted) {
          hasStarted = true;
          resolve(); // 假设服务已经在运行
        }
      }
    });

    // 处理进程退出
    childProcess.on('exit', (code) => {
      if (code !== 0 && !hasStarted) {
        reject(new Error(`${service.name} 启动失败，退出代码: ${code}`));
      }
    });

    // 超时处理
    setTimeout(() => {
      if (!hasStarted) {
        console.log(`⚠️  ${service.name} 启动超时，但进程仍在运行 (PID: ${childProcess.pid})`);
        hasStarted = true;
        resolve();
      }
    }, 5000);
  });
}

// 停止所有服务
function stopAllServices() {
  console.log('\n🛑 正在停止所有服务...');
  
  runningProcesses.forEach(service => {
    if (service.process && service.process.pid) {
      console.log(`   停止 ${service.name}...`);
      try {
        service.process.kill('SIGTERM');
        setTimeout(() => {
          if (!service.process.killed) {
            service.process.kill('SIGKILL');
          }
        }, 2000);
      } catch (error) {
        console.error(`   停止 ${service.name} 时出错: ${error.message}`);
      }
    }
  });
  
  setTimeout(() => {
    console.log('✅ 所有服务已停止');
    process.exit(0);
  }, 3000);
}

// 设置信号处理
process.on('SIGINT', () => {
  console.log('\n收到停止信号...');
  stopAllServices();
});

process.on('SIGTERM', () => {
  console.log('\n收到终止信号...');
  stopAllServices();
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  stopAllServices();
});

// 主函数
async function main() {
  try {
    console.log('🎯 启动所有翻译服务...\n');
    
    // 按顺序启动服务
    for (let i = 0; i < services.length; i++) {
      const service = services[i];
      try {
        await startService(service);
        if (i < services.length - 1) {
          console.log('   等待2秒后启动下一个服务...\n');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        console.error(`❌ ${service.name} 启动失败: ${error.message}`);
        // 继续启动其他服务
      }
    }
    
    console.log('\n🎉 服务启动流程完成！');
    console.log('\n📋 服务地址:');
    services.forEach(service => {
      console.log(`   ${service.name}: http://localhost:${service.port}`);
    });
    
    console.log('\n🌐 主要访问地址:');
    console.log('   Web界面: http://localhost:3000');
    console.log('   健康检查: http://localhost:3000/health');
    console.log('   API状态: http://localhost:3000/api/workflow/status');
    
    console.log('\n💡 使用提示:');
    console.log('   - 按 Ctrl+C 停止所有服务');
    console.log('   - 查看 ./logs/ 目录获取详细日志');
    console.log('   - 导入 n8n-translation-workflow.json 到N8N中使用');
    console.log('   - 如果某个服务启动失败，可以单独启动该服务');
    
    console.log('\n📝 单独启动命令:');
    console.log('   PDF服务: node pdf-extraction-service.js');
    console.log('   PineCone服务: node pinecone-service.js');
    console.log('   翻译服务: node multi-agent-translator.js');
    console.log('   文档处理服务: node document-processor.js');
    console.log('   主服务: node index.js');
    
    // 保持进程运行
    process.stdin.resume();
    
  } catch (error) {
    console.error(`❌ 启动过程中发生错误: ${error.message}`);
    stopAllServices();
  }
}

// 运行主函数
main().catch(error => {
  console.error('启动失败:', error);
  process.exit(1);
});
