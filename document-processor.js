const express = require('express');
const mammoth = require('mammoth');
const { Document, Packer, Paragraph, TextRun, HeadingLevel } = require('docx');
const fs = require('fs');
const path = require('path');

class DocumentProcessor {
  constructor() {
    this.supportedFormats = ['docx', 'pdf', 'markdown'];
  }

  // 解析DOCX文档并保持格式信息
  async parseDocxWithFormat(buffer) {
    try {
      // 提取原始文本和格式信息
      const textResult = await mammoth.extractRawText({ buffer });
      const htmlResult = await mammoth.convertToHtml({ buffer });

      // 分析文档结构
      const structure = this.analyzeDocumentStructure(htmlResult.value);

      return {
        rawText: textResult.value,
        htmlContent: htmlResult.value,
        structure: structure,
        messages: textResult.messages.concat(htmlResult.messages)
      };
    } catch (error) {
      console.error('解析DOCX文档失败:', error);
      throw error;
    }
  }

  // 分析文档结构
  analyzeDocumentStructure(htmlContent) {
    const structure = {
      headings: [],
      paragraphs: [],
      lists: [],
      tables: [],
      formatting: []
    };

    // 提取标题
    const headingMatches = htmlContent.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi);
    if (headingMatches) {
      headingMatches.forEach((match, index) => {
        const level = parseInt(match.match(/<h([1-6])/)[1]);
        const text = match.replace(/<[^>]*>/g, '').trim();
        structure.headings.push({
          level: level,
          text: text,
          position: index
        });
      });
    }

    // 提取段落
    const paragraphMatches = htmlContent.match(/<p[^>]*>(.*?)<\/p>/gi);
    if (paragraphMatches) {
      paragraphMatches.forEach((match, index) => {
        const text = match.replace(/<[^>]*>/g, '').trim();
        if (text) {
          structure.paragraphs.push({
            text: text,
            position: index,
            hasFormatting: /<(strong|em|b|i|u)/.test(match)
          });
        }
      });
    }

    // 提取列表
    const listMatches = htmlContent.match(/<(ul|ol)[^>]*>(.*?)<\/(ul|ol)>/gi);
    if (listMatches) {
      listMatches.forEach((match, index) => {
        const items = match.match(/<li[^>]*>(.*?)<\/li>/gi);
        if (items) {
          structure.lists.push({
            type: match.includes('<ul') ? 'unordered' : 'ordered',
            items: items.map(item => item.replace(/<[^>]*>/g, '').trim()),
            position: index
          });
        }
      });
    }

    return structure;
  }

  // 将翻译结果重新组装成DOCX
  async generateDocx(translatedSegments, originalStructure, outputPath) {
    try {
      const doc = new Document({
        sections: [{
          properties: {},
          children: this.buildDocumentChildren(translatedSegments, originalStructure)
        }]
      });

      const buffer = await Packer.toBuffer(doc);

      if (outputPath) {
        fs.writeFileSync(outputPath, buffer);
      }

      return buffer;
    } catch (error) {
      console.error('生成DOCX文档失败:', error);
      throw error;
    }
  }

  // 构建文档子元素
  buildDocumentChildren(translatedSegments, structure) {
    const children = [];
    let segmentIndex = 0;

    // 处理标题和段落
    for (const heading of structure.headings) {
      children.push(
        new Paragraph({
          text: translatedSegments[segmentIndex]?.translation || heading.text,
          heading: this.getHeadingLevel(heading.level)
        })
      );
      segmentIndex++;
    }

    for (const paragraph of structure.paragraphs) {
      const translatedText = translatedSegments[segmentIndex]?.translation || paragraph.text;

      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: translatedText,
              bold: paragraph.hasFormatting
            })
          ]
        })
      );
      segmentIndex++;
    }

    // 处理列表
    for (const list of structure.lists) {
      list.items.forEach(item => {
        const translatedItem = translatedSegments[segmentIndex]?.translation || item;
        children.push(
          new Paragraph({
            text: `• ${translatedItem}`,
            bullet: {
              level: 0
            }
          })
        );
        segmentIndex++;
      });
    }

    return children;
  }

  // 获取标题级别
  getHeadingLevel(level) {
    const levels = {
      1: HeadingLevel.HEADING_1,
      2: HeadingLevel.HEADING_2,
      3: HeadingLevel.HEADING_3,
      4: HeadingLevel.HEADING_4,
      5: HeadingLevel.HEADING_5,
      6: HeadingLevel.HEADING_6
    };
    return levels[level] || HeadingLevel.HEADING_1;
  }

  // 生成Markdown格式
  generateMarkdown(translatedSegments, originalStructure) {
    let markdown = '';
    let segmentIndex = 0;

    // 添加标题
    for (const heading of originalStructure.headings) {
      const translatedText = translatedSegments[segmentIndex]?.translation || heading.text;
      const headingPrefix = '#'.repeat(heading.level);
      markdown += `${headingPrefix} ${translatedText}\n\n`;
      segmentIndex++;
    }

    // 添加段落
    for (const paragraph of originalStructure.paragraphs) {
      const translatedText = translatedSegments[segmentIndex]?.translation || paragraph.text;
      markdown += `${translatedText}\n\n`;
      segmentIndex++;
    }

    // 添加列表
    for (const list of originalStructure.lists) {
      list.items.forEach(item => {
        const translatedItem = translatedSegments[segmentIndex]?.translation || item;
        const listPrefix = list.type === 'ordered' ? '1.' : '-';
        markdown += `${listPrefix} ${translatedItem}\n`;
        segmentIndex++;
      });
      markdown += '\n';
    }

    return markdown;
  }

  // 分割文档为可翻译的段落
  segmentDocumentForTranslation(documentContent, structure) {
    const segments = [];

    // 添加标题段落
    structure.headings.forEach(heading => {
      segments.push({
        type: 'heading',
        level: heading.level,
        content: heading.text,
        position: heading.position
      });
    });

    // 添加正文段落
    structure.paragraphs.forEach(paragraph => {
      if (paragraph.text.length > 10) { // 过滤太短的段落
        segments.push({
          type: 'paragraph',
          content: paragraph.text,
          position: paragraph.position,
          hasFormatting: paragraph.hasFormatting
        });
      }
    });

    // 添加列表项
    structure.lists.forEach(list => {
      list.items.forEach(item => {
        segments.push({
          type: 'list_item',
          listType: list.type,
          content: item,
          position: list.position
        });
      });
    });

    // 按位置排序
    segments.sort((a, b) => a.position - b.position);

    return segments;
  }
}

// Express服务器
const app = express();
const port = process.env.PROCESSOR_SERVICE_PORT || 3004;

app.use(express.json({ limit: '50mb' }));

const processor = new DocumentProcessor();

// 解析文档
app.post('/api/parse-document', async (req, res) => {
  try {
    const { fileData, fileType } = req.body;

    if (fileType !== 'docx') {
      return res.status(400).json({ error: '目前只支持DOCX格式' });
    }

    const buffer = Buffer.from(fileData, 'base64');
    const result = await processor.parseDocxWithFormat(buffer);

    // 分割为可翻译的段落
    const segments = processor.segmentDocumentForTranslation(result.rawText, result.structure);

    res.json({
      success: true,
      content: result.rawText,
      structure: result.structure,
      segments: segments,
      segmentCount: segments.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 生成翻译后的文档
app.post('/api/generate-document', async (req, res) => {
  try {
    const { translatedSegments, originalStructure, outputFormat = 'docx' } = req.body;

    let result;
    let contentType;
    let filename;

    if (outputFormat === 'docx') {
      result = await processor.generateDocx(translatedSegments, originalStructure);
      contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      filename = 'translated_document.docx';
    } else if (outputFormat === 'markdown') {
      result = processor.generateMarkdown(translatedSegments, originalStructure);
      contentType = 'text/markdown';
      filename = 'translated_document.md';
    } else {
      return res.status(400).json({ error: '不支持的输出格式' });
    }

    res.json({
      success: true,
      content: outputFormat === 'docx' ? result.toString('base64') : result,
      contentType: contentType,
      filename: filename,
      size: outputFormat === 'docx' ? result.length : Buffer.byteLength(result, 'utf8')
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'OK', service: '文档处理服务' });
});

// 只在直接运行时启动服务器
if (require.main === module) {
  app.listen(port, () => {
    console.log(`文档处理服务运行在端口 ${port}`);
  });
}

module.exports = { DocumentProcessor, app };
