const express = require('express');
const multer = require('multer');
const pdfParse = require('pdf-parse');
const mammoth = require('mammoth');
const fs = require('fs');
const path = require('path');

const app = express();
const port = process.env.PDF_SERVICE_PORT || 3001;

// 配置文件上传
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// PDF文本提取端点
app.post('/api/extract-pdf', upload.single('file'), async (req, res) => {
  try {
    const { fileData, fileType, language } = req.body;

    let extractedText = '';
    let metadata = {};

    if (fileType === 'pdf') {
      // 处理PDF文件
      const buffer = Buffer.from(fileData, 'base64');
      const pdfData = await pdfParse(buffer);

      extractedText = pdfData.text;
      metadata = {
        pages: pdfData.numpages,
        info: pdfData.info,
        version: pdfData.version
      };

      // 清理和格式化文本
      extractedText = cleanPdfText(extractedText, language);

    } else if (fileType === 'docx') {
      // 处理DOCX文件
      const buffer = Buffer.from(fileData, 'base64');
      const result = await mammoth.extractRawText({ buffer: buffer });

      extractedText = result.value;
      metadata = {
        messages: result.messages,
        type: 'docx'
      };

      // 清理和格式化文本
      extractedText = cleanDocxText(extractedText, language);
    }

    // 分段处理
    const segments = segmentText(extractedText, language);

    res.json({
      success: true,
      content: extractedText,
      segments: segments,
      metadata: metadata,
      language: language,
      wordCount: extractedText.split(/\s+/).length,
      characterCount: extractedText.length
    });

  } catch (error) {
    console.error('文档提取错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 清理PDF文本
function cleanPdfText(text, language) {
  // 移除多余的空白字符
  text = text.replace(/\s+/g, ' ');

  // 移除页眉页脚（通常包含页码等）
  text = text.replace(/^\d+\s*$/gm, '');

  // 处理换行符
  text = text.replace(/\n\s*\n/g, '\n\n');

  if (language === 'english') {
    // 英文特殊处理
    text = text.replace(/([a-z])([A-Z])/g, '$1 $2'); // 处理连在一起的单词
    text = text.replace(/([.!?])\s*([A-Z])/g, '$1\n\n$2'); // 句子分段
  } else if (language === 'traditional_chinese') {
    // 繁体中文特殊处理
    text = text.replace(/([。！？])\s*([^\s])/g, '$1\n\n$2'); // 中文句子分段
  }

  return text.trim();
}

// 清理DOCX文本
function cleanDocxText(text, language) {
  // 保持原有格式，只做基本清理
  text = text.replace(/\r\n/g, '\n');
  text = text.replace(/\n{3,}/g, '\n\n');

  return text.trim();
}

// 文本分段
function segmentText(text, language) {
  const segments = [];

  if (language === 'english') {
    // 按段落分割英文文本
    const paragraphs = text.split(/\n\s*\n/);
    paragraphs.forEach((para, index) => {
      if (para.trim()) {
        segments.push({
          id: index + 1,
          type: 'paragraph',
          content: para.trim(),
          wordCount: para.split(/\s+/).length
        });
      }
    });
  } else if (language === 'traditional_chinese') {
    // 按段落分割中文文本
    const paragraphs = text.split(/\n\s*\n/);
    paragraphs.forEach((para, index) => {
      if (para.trim()) {
        segments.push({
          id: index + 1,
          type: 'paragraph',
          content: para.trim(),
          characterCount: para.length
        });
      }
    });
  }

  return segments;
}

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'OK', service: 'PDF提取服务' });
});

// 只在直接运行时启动服务器
if (require.main === module) {
  app.listen(port, () => {
    console.log(`PDF提取服务运行在端口 ${port}`);
  });
}

module.exports = app;
