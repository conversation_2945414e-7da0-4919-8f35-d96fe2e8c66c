#!/usr/bin/env node

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const ports = [3000, 3001, 3002, 3003, 3004];

async function killProcessOnPort(port) {
  try {
    console.log(`🔍 检查端口 ${port}...`);
    
    // 在Windows上查找占用端口的进程
    const { stdout } = await execAsync(`netstat -ano | findstr :${port}`);
    
    if (stdout.trim()) {
      console.log(`   端口 ${port} 被占用:`);
      console.log(`   ${stdout.trim()}`);
      
      // 提取PID
      const lines = stdout.trim().split('\n');
      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 5) {
          const pid = parts[parts.length - 1];
          if (pid && pid !== '0') {
            console.log(`   尝试终止进程 PID: ${pid}`);
            try {
              await execAsync(`taskkill /F /PID ${pid}`);
              console.log(`   ✅ 成功终止进程 ${pid}`);
            } catch (killError) {
              console.log(`   ⚠️  无法终止进程 ${pid}: ${killError.message}`);
            }
          }
        }
      }
    } else {
      console.log(`   ✅ 端口 ${port} 空闲`);
    }
  } catch (error) {
    if (error.message.includes('找不到')) {
      console.log(`   ✅ 端口 ${port} 空闲`);
    } else {
      console.log(`   ❌ 检查端口 ${port} 时出错: ${error.message}`);
    }
  }
}

async function cleanupAllPorts() {
  console.log('🧹 清理翻译服务端口...\n');
  
  for (const port of ports) {
    await killProcessOnPort(port);
    console.log('');
  }
  
  console.log('✅ 端口清理完成！');
  console.log('💡 现在可以运行 node start-services-fixed.js 启动服务');
}

// 运行清理
cleanupAllPorts().catch(error => {
  console.error('清理失败:', error);
  process.exit(1);
});
