import type { <PERSON><PERSON>base<PERSON>ilter, SupabaseFilterRPCCall, SupabaseMetadata } from "@langchain/community/vectorstores/supabase";
import { StructuredQuery } from "@langchain/core/structured_query";
/**
 * Utility class used to duplicate parameters for a proxy object,
 * specifically designed to work with `SupabaseFilter` objects. It
 * contains methods to handle different types of operations such as "or",
 * "filter", "in", "contains", "textSearch", "match", "not", and default
 * operations.
 */
export declare class ProxyParamsDuplicator {
    duplicationAllowedOps: string[];
    values: [string, string][];
    /**
     * Creates a proxy handler for a `SupabaseFilter` object. The handler
     * intercepts get operations and applies specific logic based on the
     * property being accessed.
     * @returns A proxy handler for a `SupabaseFilter` object.
     */
    buildProxyHandler(): ProxyHandler<SupabaseFilter>;
    /**
     * Removes type annotations from a value string.
     * @param value The value string to clean.
     * @returns The cleaned value string.
     */
    removeType(value: string): string;
    /**
     * Adds a default operation clause to the values array.
     * @param prop The operation property.
     * @param column The column to apply the operation to.
     * @param value The value for the operation.
     */
    addDefaultOpClause(prop: string, column: string, value: unknown): void;
    /**
     * Adds an 'or' clause to the values array.
     * @param filters The filters for the 'or' clause.
     * @param foreignTable Optional foreign table for the 'or' clause.
     */
    addOrClause(filters: string, { foreignTable }?: {
        foreignTable?: string;
    }): void;
    /**
     * Adds a 'filter' clause to the values array.
     * @param column The column to apply the filter to.
     * @param operator The operator for the filter.
     * @param value The value for the filter.
     */
    addFilterClause(column: string, operator: string, value: unknown): void;
    /**
     * Adds an 'in' clause to the values array.
     * @param column The column to apply the 'in' clause to.
     * @param values The values for the 'in' clause.
     */
    addInClause(column: string, values: unknown[]): void;
    /**
     * Adds a 'contains' clause to the values array.
     * @param column The column to apply the 'contains' clause to.
     * @param value The value for the 'contains' clause.
     */
    addContainsClause(column: string, value: unknown): void;
    /**
     * Adds a 'textSearch' clause to the values array.
     * @param column The column to apply the 'textSearch' clause to.
     * @param query The query for the 'textSearch' clause.
     * @param config Optional configuration for the 'textSearch' clause.
     * @param type Optional type for the 'textSearch' clause.
     */
    addTextSearchClause(column: string, query: string[], { config, type, }?: {
        config?: string;
        type?: "plain" | "phrase" | "websearch";
    }): void;
    /**
     * Adds a 'not' clause to the values array.
     * @param column The column to apply the 'not' clause to.
     * @param operator The operator for the 'not' clause.
     * @param value The value for the 'not' clause.
     */
    addNotClause(column: string, operator: string, value: unknown): void;
    /**
     * Adds a 'match' clause to the values array.
     * @param query The query for the 'match' clause.
     */
    addMatchClause(query: Record<string, unknown>): void;
    /**
     * Returns the flattened parameters as a string.
     * @returns The flattened parameters as a string.
     */
    flattenedParams(): string;
    /**
     * Gets flattened parameters from a `SupabaseFilter` and a
     * `SupabaseFilterRPCCall`.
     * @param rpc The `SupabaseFilter` object.
     * @param filter The `SupabaseFilterRPCCall` object.
     * @returns The flattened parameters as a string.
     */
    static getFlattenedParams(rpc: SupabaseFilter, filter: SupabaseFilterRPCCall): string;
}
/**
 * Converts a `SupabaseMetadata` object into a `StructuredQuery` object.
 * The function creates a new `StructuredQuery` object and uses the
 * `Operation` and `Comparison` classes to build the query.
 */
export declare function convertObjectFilterToStructuredQuery(objFilter: SupabaseMetadata): StructuredQuery;
