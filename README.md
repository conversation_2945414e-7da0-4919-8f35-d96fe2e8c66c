# N8N 精准文档翻译工作流

这是一个基于N8N的高级文档翻译工作流，实现英文与繁体中文之间的精准互译，特别适用于长篇技术文档的翻译。

## 🌟 主要特性

- **精准翻译**: 基于参考文档的术语对照，确保翻译一致性
- **多智能体系统**: 翻译员 + 校对员 + 术语检查员的协作模式
- **格式保持**: 完整保留原文档的格式、结构和样式
- **知识库集成**: 使用PineCone向量数据库存储和查询翻译知识
- **长文档支持**: 优化处理近200页的大型文档
- **多格式输出**: 支持DOCX和Markdown格式输出

## 🏗️ 系统架构

```
用户表单 → 文件验证 → 参考文档解析 → 知识库存储 → 文档分割 → 多智能体翻译 → 格式重建 → 结果输出
```

### 核心组件

1. **PDF提取服务** (端口3001) - 解析PDF和DOCX文档
2. **PineCone服务** (端口3002) - 知识库管理和相似度查询
3. **多智能体翻译服务** (端口3003) - AI翻译和质量控制
4. **文档处理服务** (端口3004) - 格式解析和重建
5. **工作流管理器** (端口3000) - 统一API和流程控制

## 📋 系统要求

- Node.js >= 18.0.0
- npm >= 8.0.0
- OpenAI API密钥
- PineCone账户和API密钥
- N8N环境 (可选)

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

复制环境变量模板并填入您的API密钥：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# 基础配置
OPENAI_API_KEY=your_openai_api_key_here
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here
PINECONE_INDEX_NAME=translation-knowledge

# OpenAI Base URL配置 (如果需要使用代理或第三方服务)
OPENAI_BASE_URL=https://api.openai.com/v1

# 常见代理服务示例:
# OPENAI_BASE_URL=https://your-proxy-domain.com/v1
# OPENAI_BASE_URL=https://api.openai-proxy.com/v1
```

### 3. 测试配置

在启动服务前，建议先测试OpenAI配置是否正确：

```bash
npm run test:openai
```

这个命令会：
- 验证API密钥是否有效
- 测试Base URL连接
- 验证聊天和嵌入API功能
- 提供详细的错误诊断

### 4. 启动服务

#### 推荐方式：一键启动所有服务
```bash
npm start
# 或者
npm run start:all
```

这将自动启动所有必要的服务：
- PDF提取服务 (端口 3011)
- PineCone服务 (端口 3012)
- 多智能体翻译服务 (端口 3013)
- 文档处理服务 (端口 3014)
- 主服务和Web界面 (端口 3000)

#### 方式二：分别启动各个服务
```bash
# 终端1: PDF提取服务
npm run start:pdf

# 终端2: PineCone服务
npm run start:pinecone

# 终端3: 翻译服务
npm run start:translator

# 终端4: 文档处理服务
npm run start:processor

# 终端5: 主服务
npm run start:main
```

#### 故障排除
如果遇到端口冲突，可以运行：
```bash
npm run cleanup
```

### 5. 导入N8N工作流

1. 打开N8N界面
2. 导入 `n8n-translation-workflow.json` 文件
3. 配置各个节点的服务地址
4. 激活工作流

## 📖 使用方法

### 通过N8N表单

1. 访问N8N工作流的表单URL
2. 上传以下文件：
   - 英文参考文档 (PDF)
   - 繁体中文参考文档 (PDF)
   - 待翻译文档 (DOCX或PDF)
3. 选择翻译方向和输出格式
4. 提交并等待处理完成

### 通过API调用

```bash
curl -X POST http://localhost:3000/api/translate-document \
  -H "Content-Type: application/json" \
  -d '{
    "englishRefFile": "base64_encoded_pdf_data",
    "chineseRefFile": "base64_encoded_pdf_data",
    "targetDocFile": "base64_encoded_docx_data",
    "translationDirection": "英文 → 繁体中文",
    "outputFormat": "DOCX"
  }'
```

## 🔧 配置说明

### 翻译质量控制

系统采用三层质量控制机制：

1. **翻译员**: 基于参考文档进行初始翻译
2. **术语检查员**: 验证术语翻译的一致性
3. **校对员**: 综合评估翻译质量并提供修改建议

### 知识库管理

- 参考文档自动存储到PineCone向量数据库
- 支持相似度查询和术语匹配
- 可扩展的知识库架构

### 格式保持

- 完整解析DOCX文档结构
- 保持标题、段落、列表等格式
- 支持复杂文档布局

## 📊 API文档

### 健康检查
```
GET /health
```

### 翻译文档
```
POST /api/translate-document
```

### 查询进度
```
GET /api/workflow/:workflowId/progress
```

## 🧪 测试

```bash
npm test
```

## 📝 日志

系统日志保存在 `./logs/translation.log`，可通过环境变量调整日志级别。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

MIT License

## 🆘 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 确认环境变量配置正确

2. **翻译质量不佳**
   - 确保参考文档质量良好
   - 调整翻译参数和提示词

3. **文档格式丢失**
   - 检查原文档是否为标准DOCX格式
   - 确认文档处理服务正常运行

### 性能优化

- 对于大型文档，建议分批处理
- 可以调整批处理大小和超时时间
- 考虑使用更强大的AI模型

## 📞 支持

如有问题，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至支持邮箱
- 查看项目Wiki获取更多信息
